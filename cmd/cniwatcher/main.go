package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/neuvector/network-enforcement/internal/cniwatcher"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/log/zap"
)

func main() {
	log.SetLogger(zap.New())
	ctrlLog := log.Log.WithName("cniwatcher")

	config, err := rest.InClusterConfig()
	if err != nil {
		ctrlLog.Error(err, "unable to get cluster config")
		os.Exit(1)
	}

	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		ctrlLog.Error(err, "unable to create k8s client")
		os.Exit(1)
	}

	watcher, err := cniwatcher.NewCNIWatcher(clientset, config, ctrlLog)
	if err != nil {
		ctrlLog.Error(err, "failed to create cniWatcher")
		os.Exit(1)
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	shutdownCh := make(chan struct{})

	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)
	go func() {
		<-c
		ctrlLog.Info("Received shutdown signal")
		cancel()
		close(shutdownCh)
	}()

	errCh := make(chan error, 1)
	go func() {
		errCh <- watcher.Start(ctx)
	}()

	select {
	case err := <-errCh:
		if err != nil {
			ctrlLog.Error(err, "failed to start cniWatcher")
			os.Exit(1)
		}
	case <-shutdownCh:
		shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer shutdownCancel()

		switch w := watcher.(type) {
		case *cniwatcher.AWSVPCWatcher:
			if err := w.Shutdown(shutdownCtx); err != nil {
				ctrlLog.Error(err, "error shutting down AWS VPC watcher")
			}
		case *cniwatcher.CalicoWatcher:
			if err := w.Shutdown(shutdownCtx); err != nil {
				ctrlLog.Error(err, "error shutting down Calico watcher")
			}
		case *cniwatcher.CiliumWatcher:
			if err := w.Shutdown(shutdownCtx); err != nil {
				ctrlLog.Error(err, "error shutting down Cilium watcher")
			}
		case *cniwatcher.FlannelWatcher:
			if err := w.Shutdown(shutdownCtx); err != nil {
				ctrlLog.Error(err, "error shutting down Flannel watcher")
			}
		case *cniwatcher.BaseWatcher:
			if err := w.ShutdownOtel(shutdownCtx); err != nil {
				ctrlLog.Error(err, "error shutting down OpenTelemetry")
			}
		}
	}

	ctrlLog.Info("cniWatcher exited")
}
