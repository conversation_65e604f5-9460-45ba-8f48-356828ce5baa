package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/neuvector/network-enforcement/internal/cniwatcher"
	"github.com/neuvector/network-enforcement/internal/otel"
	"github.com/sirupsen/logrus"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
)

func main() {
	ctrlLog := logrus.New()

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	otelService := otel.NewOpenTelemetryService(ctx, ctrlLog)
	if err := otelService.Start(); err != nil {
		ctrlLog.Warn("Failed to start OpenTelemetry", "err", err)
	}

	config, err := rest.InClusterConfig()
	if err != nil {
		ctrlLog.Error(err, "unable to get cluster config")
		os.Exit(1)
	}

	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		ctrlLog.Error(err, "unable to create k8s client")
		os.Exit(1)
	}

	cniWatcherCfg := cniwatcher.CNIWatcherConfig{
		Ctx:         ctx,
		Clientset:   clientset,
		Config:      config,
		Log:         ctrlLog,
		NodeName:    os.Getenv("NODE_NAME"),
		CNIType:     cniwatcher.CNIType(os.Getenv("CNI_TYPE")),
		OtelService: otelService,
	}

	cniWatcher, err := cniwatcher.NewCNIWatcher(cniWatcherCfg)
	if err != nil {
		ctrlLog.Error("Failed to create cniWatcher", "err", err)
		os.Exit(1)
	}

	shutdownCh := make(chan struct{})

	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)
	go func() {
		<-c
		ctrlLog.Info("Received shutdown signal")
		cancel()
		close(shutdownCh)
	}()

	errCh := make(chan error, 1)
	go func() {
		errCh <- cniWatcher.Start()
	}()

	select {
	case err := <-errCh:
		if err != nil {
			ctrlLog.Error("Failed to start cniWatcher", "err", err)
			os.Exit(1)
		}
	case <-shutdownCh:
		ctxOtelShutdown, cancel := context.WithTimeout(ctx, 10*time.Second)
		defer cancel()

		ctrlLog.Info("Shutting down OpenTelemetry")
		if err := otelService.Shutdown(ctxOtelShutdown); err != nil {
			ctrlLog.Error("Failed to shutdown OpenTelemetry", "err", err)
		}

		ctrlLog.Info("Shutting down %s cniWatcher", cniWatcherCfg.CNIType)
		if err := cniWatcher.Shutdown(); err != nil {
			ctrlLog.Error("Failed to shutdown cniWatcher", "err", err)
		}
	}

	ctrlLog.Info("cniWatcher exited")
}
