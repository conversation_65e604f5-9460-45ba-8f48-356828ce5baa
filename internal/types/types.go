package types

import (
	"context"
	"fmt"

	"github.com/sirupsen/logrus"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	"go.opentelemetry.io/otel/trace"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type Policy struct {
	metav1.TypeMeta `json:",inline"`
	Name            string `json:"name"`
	Namespace       string `json:"namespace"`
}

func (p Policy) String() string {
	return fmt.Sprintf("%s/%s/%s/%s", p.APIVersion, p.Kind, p.Namespace, p.Name)
}

type PolicyDenyEvent struct {
	Timestamp int64  `json:"timestamp"`
	NodeName  string `json:"node_name"`
	// e.g. "aws-vpc", "calico", "cilium", "flannel"
	CNIType string `json:"cni_type"`
	// "TCP", "UDP", "ICMP", "SCTP"
	Protocol     corev1.Protocol `json:"protocol"`
	SrcNamespace string          `json:"source_namespace"`
	SrcName      string          `json:"source_name"`
	Src<PERSON>abels    []string        `json:"source_labels"`
	DstNamespace string          `json:"destination_namespace"`
	DstName      string          `json:"destination_name"`
	DstLabels    []string        `json:"destination_labels"`
	SrcWorkloads []string        `json:"source_workloads,omitempty"`
	DstWorkloads []string        `json:"destination_workloads,omitempty"`
	// The K8s NetworkPolicies or CiliumNetworkPolicies denying the egress of the flow
	EgressEnforcedBy []Policy `json:"egress_enforced_by,omitempty"`
	// The K8s NetworkPolicies or CiliumNetworkPolicies denying the ingress of the flow
	IngressEnforcedBy []Policy `json:"ingress_enforced_by,omitempty"`
}

type OpenTelemetryService struct {
	Ctx            context.Context
	Log            *logrus.Logger
	Tracer         trace.Tracer
	TracerProvider *sdktrace.TracerProvider
}
