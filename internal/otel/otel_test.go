package otel

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/neuvector/network-enforcement/internal/types"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func TestNewOpenTelemetryService(t *testing.T) {
	log := logrus.New()
	ctx := context.Background()

	service := NewOpenTelemetryService(ctx, log)
	assert.NotNil(t, service)
	assert.NotNil(t, service.Service)
	assert.Equal(t, ctx, service.Service.Ctx)
	assert.Equal(t, log, service.Service.Log)
}

func TestOtelService_Start(t *testing.T) {
	tests := []struct {
		name             string
		collectorEnv     string
		wantErr          bool
		expectedEndpoint string
	}{
		{
			name:             "Default collector endpoint",
			collectorEnv:     "",
			wantErr:          false,
			expectedEndpoint: DefaultOtelCollectorEndpoint,
		},
		{
			name:             "Custom collector endpoint",
			collectorEnv:     "custom-collector:4317",
			wantErr:          false,
			expectedEndpoint: "custom-collector:4317",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.collectorEnv != "" {
				os.Setenv("OTEL_EXPORTER_OTLP_ENDPOINT", tt.collectorEnv)
				defer os.Unsetenv("OTEL_EXPORTER_OTLP_ENDPOINT")
			}

			log := logrus.New()
			ctx := context.Background()
			service := NewOpenTelemetryService(ctx, log)

			err := service.Start()
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				// Start will likely fail due to missing collector, but that's expected in tests
				// We're mainly testing that the method doesn't panic
				assert.NoError(t, err)
			}
		})
	}
}

func TestOtelService_EmitPolicyDenyEvent(t *testing.T) {
	log := logrus.New()
	ctx := context.Background()
	service := NewOpenTelemetryService(ctx, log)

	event := &types.PolicyDenyEvent{
		Timestamp:    time.Now().Unix(),
		NodeName:     "test-node",
		CNIType:      "test-cni",
		Protocol:     "TCP",
		SrcNamespace: "default",
		SrcName:      "test-pod",
		SrcLabels:    []string{"app=test"},
		DstNamespace: "default",
		DstName:      "web-pod",
		DstLabels:    []string{"app=web"},
		EgressEnforcedBy: []types.Policy{
			{
				TypeMeta: metav1.TypeMeta{
					APIVersion: "networking.k8s.io/v1",
					Kind:       "NetworkPolicy",
				},
				Name:      "test-policy",
				Namespace: "default",
			},
		},
	}

	// This should not panic even if OTEL is not initialized
	service.EmitPolicyDenyEvent(event)
}

func TestOtelService_Shutdown(t *testing.T) {
	log := logrus.New()
	ctx := context.Background()
	service := NewOpenTelemetryService(ctx, log)

	// Shutdown should work even if OTEL is not initialized
	err := service.Shutdown(ctx)
	assert.NoError(t, err)
}

func TestPoliciesToStrings(t *testing.T) {
	tests := []struct {
		name     string
		policies []types.Policy
		want     []string
	}{
		{
			name:     "Empty policies",
			policies: []types.Policy{},
			want:     []string{},
		},
		{
			name: "Single policy",
			policies: []types.Policy{
				{
					TypeMeta: metav1.TypeMeta{
						APIVersion: "networking.k8s.io/v1",
						Kind:       "NetworkPolicy",
					},
					Name:      "test-policy",
					Namespace: "default",
				},
			},
			want: []string{"networking.k8s.io/v1/NetworkPolicy/default/test-policy"},
		},
		{
			name: "Multiple policies",
			policies: []types.Policy{
				{
					TypeMeta: metav1.TypeMeta{
						APIVersion: "networking.k8s.io/v1",
						Kind:       "NetworkPolicy",
					},
					Name:      "policy1",
					Namespace: "default",
				},
				{
					TypeMeta: metav1.TypeMeta{
						APIVersion: "cilium.io/v2",
						Kind:       "CiliumNetworkPolicy",
					},
					Name:      "policy2",
					Namespace: "kube-system",
				},
			},
			want: []string{
				"networking.k8s.io/v1/NetworkPolicy/default/policy1",
				"cilium.io/v2/CiliumNetworkPolicy/kube-system/policy2",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := policiesToStrings(tt.policies)
			assert.Equal(t, tt.want, got)
		})
	}
}
