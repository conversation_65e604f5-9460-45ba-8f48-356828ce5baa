package otel

import (
	"context"
	"fmt"
	"os"
	"time"

	"github.com/neuvector/network-enforcement/internal/types"
	"github.com/sirupsen/logrus"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
	"go.opentelemetry.io/otel/sdk/resource"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.30.0"
)

var DefaultOtelServiceVersion = "dev"
var DefaultOtelCollectorEndpoint = "otel-collector:4317"

type OtelService struct {
	Service *types.OpenTelemetryService
}

func NewOpenTelemetryService(ctx context.Context, log *logrus.Logger) *OtelService {
	return &OtelService{
		Service: &types.OpenTelemetryService{
			Ctx: ctx,
			Log: log,
		},
	}
}

func (s *OtelService) Start() error {
	collectorEndpoint := os.Getenv("OTEL_EXPORTER_OTLP_ENDPOINT")
	if collectorEndpoint == "" {
		collectorEndpoint = DefaultOtelCollectorEndpoint
	}

	exporter, err := otlptracegrpc.New(s.Service.Ctx,
		otlptracegrpc.WithEndpoint(collectorEndpoint),
		otlptracegrpc.WithInsecure(),
	)
	if err != nil {
		return fmt.Errorf("failed to create OTLP exporter: %w", err)
	}

	res, err := resource.New(s.Service.Ctx,
		resource.WithAttributes(
			semconv.ServiceNameKey.String("cniwatcher"),
			semconv.ServiceVersionKey.String(DefaultOtelServiceVersion),
		),
	)
	if err != nil {
		return fmt.Errorf("failed to create resource: %w", err)
	}

	s.Service.TracerProvider = sdktrace.NewTracerProvider(
		sdktrace.WithBatcher(exporter),
		sdktrace.WithResource(res),
	)

	otel.SetTracerProvider(s.Service.TracerProvider)
	s.Service.Tracer = s.Service.TracerProvider.Tracer("cniwatcher")
	s.Service.Log.Info("OpenTelemetry initialized", "collector", collectorEndpoint)
	return nil
}

func policiesToStrings(policies []types.Policy) []string {
	result := make([]string, len(policies))
	for i, p := range policies {
		result[i] = p.String()
	}
	return result
}

func (s *OtelService) EmitPolicyDenyEvent(event *types.PolicyDenyEvent) {
	if s.Service.Tracer == nil {
		s.Service.Log.Warn("OpenTelemetry is not initialized, skip emitting policy deny event")
		return
	}

	ctx := context.Background()
	_, span := s.Service.Tracer.Start(ctx, "policy.deny")
	defer span.End()

	span.SetAttributes(
		attribute.String("timestamp.formatted", time.Unix(event.Timestamp, 0).Format(time.RFC3339)),
		attribute.String("node.name", event.NodeName),
		attribute.String("cni.plugin", event.CNIType),
		attribute.String("network.protocol", string(event.Protocol)),
		attribute.String("source.namespace", event.SrcNamespace),
		attribute.String("source.name", event.SrcName),
		attribute.StringSlice("source.labels", event.SrcLabels),
		attribute.StringSlice("source.workloads", event.SrcWorkloads),
		attribute.String("destination.namespace", event.DstNamespace),
		attribute.String("destination.name", event.DstName),
		attribute.StringSlice("destination.labels", event.DstLabels),
		attribute.StringSlice("destination.workloads", event.DstWorkloads),
		attribute.StringSlice("egress.enforced_by", policiesToStrings(event.EgressEnforcedBy)),
		attribute.StringSlice("ingress.enforced_by", policiesToStrings(event.IngressEnforcedBy)),
	)
}

func (s *OtelService) Shutdown(ctx context.Context) error {
	if s.Service.TracerProvider == nil {
		return nil
	}

	return s.Service.TracerProvider.Shutdown(ctx)
}
