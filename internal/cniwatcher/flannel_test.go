package cniwatcher

import (
	"context"
	"testing"
	"time"

	"github.com/go-logr/logr"
	"github.com/stretchr/testify/assert"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes/fake"
	"k8s.io/client-go/rest"
)

func TestNewFlannelWatcher(t *testing.T) {
	tests := []struct {
		name     string
		nodeName string
		wantErr  bool
	}{
		{
			name:     "Valid node name",
			nodeName: "test-node",
			wantErr:  false,
		},
		{
			name:     "Empty node name",
			nodeName: "",
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client := fake.NewSimpleClientset()
			config := &rest.Config{}
			log := logr.Discard()

			watcher, err := NewFlannelWatcher(client, config, log, tt.nodeName)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, watcher)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, watcher)
				assert.Equal(t, CNITypeFlannel, watcher.cniType)
			}
		})
	}
}

func TestFlannelWatcher_Start(t *testing.T) {
	tests := []struct {
		name    string
		wantErr bool
	}{
		{
			name:    "Successful start",
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client := fake.NewSimpleClientset()
			watcher := &FlannelWatcher{
				BaseWatcher: BaseWatcher{
					clientset: client,
					log:       logr.Discard(),
					nodeName:  "test-node",
					cniType:   CNITypeFlannel,
				},
			}

			ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
			defer cancel()

			err := watcher.Start(ctx)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestFlannelWatcher_ParsePolicyDenyEvent(t *testing.T) {
	tests := []struct {
		name     string
		flowData string
		wantErr  bool
	}{
		{
			name:     "Valid policy deny event",
			flowData: "Jan 1 12:00:00 DROP by policy default/test-policy IN=eth0 OUT=eth1 MAC=00:11:22:33:44:55 SRC=******** DST=10.0.0.2 PROTO=TCP SPT=12345 DPT=80",
			wantErr:  false,
		},
		{
			name:     "Non-matching log line",
			flowData: "Jan 1 12:00:00 Some other log message",
			wantErr:  false,
		},
		{
			name:     "Empty log line",
			flowData: "",
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			watcher := &FlannelWatcher{
				BaseWatcher: BaseWatcher{
					log: logr.Discard(),
				},
			}

			err := watcher.parsePolicyDenyEvent(tt.flowData)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestFlannelWatcher_GetEndpointInfoByIP(t *testing.T) {
	tests := []struct {
		name    string
		ip      string
		pods    []*corev1.Pod
		wantErr bool
	}{
		{
			name: "Valid IP address",
			ip:   "********",
			pods: []*corev1.Pod{
				{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "test-pod",
						Namespace: "default",
						Labels: map[string]string{
							"app": "test",
						},
					},
					Status: corev1.PodStatus{
						PodIP: "********",
					},
				},
			},
			wantErr: false,
		},
		{
			name:    "Invalid IP address",
			ip:      "invalid-ip",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client := fake.NewSimpleClientset()
			for _, pod := range tt.pods {
				_, err := client.CoreV1().Pods(pod.Namespace).Create(context.Background(), pod, metav1.CreateOptions{})
				assert.NoError(t, err)
			}

			watcher := &FlannelWatcher{
				BaseWatcher: BaseWatcher{
					clientset: client,
					log:       logr.Discard(),
				},
			}

			info, err := watcher.getEndpointInfoByIP(tt.ip)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, info)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, info)
				assert.Equal(t, tt.pods[0].Name, info.Name)
				assert.Equal(t, tt.pods[0].Namespace, info.Namespace)
				assert.Equal(t, []string{"app=test"}, info.Labels)
			}
		})
	}
}

func TestFlannelWatcher_Shutdown(t *testing.T) {
	tests := []struct {
		name    string
		wantErr bool
	}{
		{
			name:    "Successful shutdown",
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client := fake.NewSimpleClientset()
			watcher := &FlannelWatcher{
				BaseWatcher: BaseWatcher{
					clientset: client,
					log:       logr.Discard(),
				},
			}

			ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
			defer cancel()

			err := watcher.Shutdown(ctx)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
