package cniwatcher

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/jdrews/go-tailer/fswatcher"
	"github.com/jdrews/go-tailer/glob"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

const (
	flannelLogPath = "/var/log/ulog/syslogemu.log"
)

type FlannelWatcher struct {
	otelService     *OpenTelemetryService
	policyService *PolicyEventService
}

// Regex to match DROP by policy lines and extract fields, with optional SPT/DPT for TCP/UDP
var dropByPolicyRegex = regexp.MustCompile(`(?P<timestamp>^\w+\s+\d+\s+\d+:\d+:\d+)\s+[^ ]+\s+DROP by policy (?P<policy>[\w-]+\/[\w-]+) IN=[^ ]* OUT=[^ ]* MAC=[^ ]* SRC=(?P<srcip>[^ ]+) DST=(?P<dstip>[^ ]+).*?PROTO=(?P<proto>[^ ]+)(?: SPT=(?P<srcport>\d+) DPT=(?P<dstport>\d+))?`)

func NewFlannelWatcher(cfg CNIWatcherConfig) (*FlannelWatcher, error) {
	if cfg.NodeName == "" {
		return nil, fmt.Errorf("node name cannot be empty")
	}

	telemetry := NewOpenTelemetryService(cfg.NodeName, CNITypeFlannel, cfg.Log)
	policyService := NewPolicyEventService(cfg.Clientset, cfg.Config, telemetry)

	watcher := &FlannelWatcher{
		telemetry:     telemetry,
		policyService: policyService,
	}

	if err := telemetry.Init(context.Background()); err != nil {
		cfg.Log.Error(err, "Failed to initialize OpenTelemetry, continuing without tracing")
	}

	return watcher, nil
}

func (w *FlannelWatcher) Start(ctx context.Context) error {
	w.policyService.log.Info("Starting cniWatcher", "node", w.telemetry.nodeName, "cniType", w.telemetry.cniType)

	parsedGlob, err := glob.Parse(flannelLogPath)
	if err != nil {
		return fmt.Errorf("failed to parse log path: %w", err)
	}

	tailer, err := fswatcher.RunFileTailer([]glob.Glob{parsedGlob}, false, true, w.log)
	if err != nil {
		return fmt.Errorf("failed to start tailer: %w", err)
	}

	for {
		select {
		case <-ctx.Done():
			w.log.Info("Flannel cniWatcher shutting down due to context cancel")
			return nil
		case line := <-tailer.Lines():
			if line.Line == "" {
				continue
			}
			if err := w.parsePolicyDenyEvent(line.Line); err != nil {
				w.log.Error(err, "failed to parse policy deny event", "line", line.Line)
			}
		}
	}
}

func (w *FlannelWatcher) parsePolicyDenyEvent(line string) error {
	if line == "" {
		return fmt.Errorf("empty flow data")
	}

	matches := dropByPolicyRegex.FindStringSubmatch(line)
	if matches == nil {
		return nil
	}

	groupNames := dropByPolicyRegex.SubexpNames()
	fields := map[string]string{}
	for i, name := range groupNames {
		if i != 0 && name != "" {
			fields[name] = matches[i]
		}
	}

	// Parse timestamp (Flannel log does not have year, so use current year)
	timestampStr := fields["timestamp"]
	timeLayout := "Jan 2 15:04:05"
	t, err := time.Parse(timeLayout, timestampStr)
	if err != nil {
		w.log.Error(err, "failed to parse timestamp", "timestamp", timestampStr)
		t = time.Now()
	} else {
		now := time.Now()
		t = t.AddDate(now.Year(), 0, 0)
	}

	policyParts := strings.SplitN(fields["policy"], "/", 2)
	var policyNamespace, policyName, policyKind string
	if len(policyParts) == 2 {
		policyNamespace, policyName = policyParts[0], policyParts[1]
		policyKind = "NetworkPolicy"
	}

	srcIP := fields["srcip"]
	dstIP := fields["dstip"]
	proto := fields["proto"]

	srcEndpointInfo, err := w.policyService.GetEndpointInfoByIP(srcIP)
	if err != nil {
		w.log.Error(err, "failed to get source endpoint info", "ip", srcIP)
	}
	dstEndpointInfo, err := w.policyService.GetEndpointInfoByIP(dstIP)
	if err != nil {
		w.log.Error(err, "failed to get destination endpoint info", "ip", dstIP)
	}

	apiVersion, err := w.policyService.GetNetworkPolicyAPIVersion(policyKind, policyName, policyNamespace)
	if err != nil {
		w.log.Error(err, "Failed to get API version for policy", "policyKind", policyKind, "policyName", policyName, "policyNamespace", policyNamespace)
	}

	// TODO: there is no reliable way to determine the enforce direction,
	//       use EgressEnforcedBy to report policy for now
	event := &PolicyDenyEvent{
		Timestamp:    t.Unix(),
		CNIType:      string(w.telemetry.cniType),
		Protocol:     corev1.Protocol(proto),
		SrcNamespace: srcEndpointInfo.Namespace,
		SrcName:      srcEndpointInfo.Name,
		SrcLabels:    srcEndpointInfo.Labels,
		DstNamespace: dstEndpointInfo.Namespace,
		DstName:      dstEndpointInfo.Name,
		DstLabels:    dstEndpointInfo.Labels,
		EgressEnforcedBy: []Policy{{
			TypeMeta:  metav1.TypeMeta{APIVersion: apiVersion, Kind: policyKind},
			Name:      policyName,
			Namespace: policyNamespace,
		}},
	}

	err = w.policyService.ProcessPolicyDenyEvent(event)
	if err != nil {
		return fmt.Errorf("failed to process PolicyDenyEvent: %w", err)
	}

	return nil
}

func (w *FlannelWatcher) Shutdown(ctx context.Context) error {
	w.log.Info("Shutting down Flannel cniWatcher")

	if err := w.telemetry.Shutdown(ctx); err != nil {
		w.log.Error(err, "error shutting down OpenTelemetry")
		return err
	}

	return nil
}
