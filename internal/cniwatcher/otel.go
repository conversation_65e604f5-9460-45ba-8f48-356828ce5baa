package cniwatcher

import (
	"context"
	"fmt"
	"os"
	"time"

	"github.com/sirupsen/logrus"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
	"go.opentelemetry.io/otel/sdk/resource"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.30.0"
	"go.opentelemetry.io/otel/trace"
)

var DefaultOtelServiceVersion = "dev"
var DefaultOtelCollectorEndpoint = "otel-collector:4317"

type OpenTelemetryService struct {
	nodeName       string
	cniType        CNIType
	log            *logrus.Logger
	tracer         trace.Tracer
	tracerProvider *sdktrace.TracerProvider
}

func NewOpenTelemetryService(nodeName string, cniType CNIType, log *logrus.Logger) *OpenTelemetryService {
	return &OpenTelemetryService{
		nodeName: nodeName,
		cniType:  cniType,
		log:      log,
	}
}

func (t *OpenTelemetryService) Init(ctx context.Context) error {
	collectorEndpoint := os.Getenv("OTEL_EXPORTER_OTLP_ENDPOINT")
	if collectorEndpoint == "" {
		collectorEndpoint = DefaultOtelCollectorEndpoint
	}

	exporter, err := otlptracegrpc.New(ctx,
		otlptracegrpc.WithEndpoint(collectorEndpoint),
		otlptracegrpc.WithInsecure(),
	)
	if err != nil {
		return fmt.Errorf("failed to create OTLP exporter: %w", err)
	}

	res, err := resource.New(ctx,
		resource.WithAttributes(
			semconv.ServiceNameKey.String("cniwatcher"),
			semconv.ServiceVersionKey.String(DefaultOtelServiceVersion),
			attribute.String("node.name", t.nodeName),
			attribute.String("cni.type", string(t.cniType)),
		),
	)
	if err != nil {
		return fmt.Errorf("failed to create resource: %w", err)
	}

	t.tracerProvider = sdktrace.NewTracerProvider(
		sdktrace.WithBatcher(exporter),
		sdktrace.WithResource(res),
	)

	otel.SetTracerProvider(t.tracerProvider)
	t.tracer = t.tracerProvider.Tracer("cniwatcher")
	t.log.Info("OpenTelemetry initialized", "collector", collectorEndpoint)
	return nil
}

func (t *OpenTelemetryService) EmitPolicyDenyEvent(event *PolicyDenyEvent) {
	if t.tracer == nil {
		t.log.Warn("OpenTelemetry is not initialized, skip emitting policy deny event")
		return
	}

	ctx := context.Background()
	_, span := t.tracer.Start(ctx, "policy.deny")
	defer span.End()

	span.SetAttributes(
		attribute.String("timestamp.formatted", time.Unix(event.Timestamp, 0).Format(time.RFC3339)),
		attribute.String("node.name", t.nodeName),
		attribute.String("cni.plugin", event.CNIType),
		attribute.String("network.protocol", string(event.Protocol)),
		attribute.String("source.namespace", event.SrcNamespace),
		attribute.String("source.name", event.SrcName),
		attribute.StringSlice("source.labels", event.SrcLabels),
		attribute.StringSlice("source.workloads", event.SrcWorkloads),
		attribute.String("destination.namespace", event.DstNamespace),
		attribute.String("destination.name", event.DstName),
		attribute.StringSlice("destination.labels", event.DstLabels),
		attribute.StringSlice("destination.workloads", event.DstWorkloads),
		attribute.StringSlice("egress.enforced_by", policiesToStrings(event.EgressEnforcedBy)),
		attribute.StringSlice("ingress.enforced_by", policiesToStrings(event.IngressEnforcedBy)),
	)
}

func (t *OpenTelemetryService) Shutdown(ctx context.Context) error {
	if t.tracerProvider == nil {
		return nil
	}
	t.log.Info("Shutting down OpenTelemetry")
	return t.tracerProvider.Shutdown(ctx)
}
