package cniwatcher

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	flowpb "github.com/cilium/cilium/api/v1/flow"
	hubbleObserver "github.com/cilium/cilium/api/v1/observer"
	monitorApi "github.com/cilium/cilium/pkg/monitor/api"
	"github.com/neuvector/network-enforcement/internal/otel"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc"
	"k8s.io/client-go/kubernetes/fake"
	"k8s.io/client-go/rest"
)

func TestNewCiliumWatcher(t *testing.T) {
	tests := []struct {
		name             string
		hubbleEnv        string
		expectedEndpoint string
	}{
		{
			name:             "Default Hubble endpoint",
			hubbleEnv:        "",
			expectedEndpoint: DefaultHubbleEndpoint,
		},
		{
			name:             "Custom Hubble endpoint",
			hubbleEnv:        "custom-hubble:4244",
			expectedEndpoint: "custom-hubble:4244",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.hubbleEnv != "" {
				os.Setenv("HUBBLE_ENDPOINT", tt.hubbleEnv)
				defer os.Unsetenv("HUBBLE_ENDPOINT")
			}

			fakeClient := fake.NewSimpleClientset()
			config := &rest.Config{}
			log := logrus.New()
			otelService := otel.NewOpenTelemetryService(context.Background(), log)

			cfg := CNIWatcherConfig{
				Ctx:         context.Background(),
				Clientset:   fakeClient,
				Config:      config,
				Log:         log,
				NodeName:    "test-node",
				CNIType:     CNITypeCilium,
				OtelService: otelService,
			}
			watcher, err := NewCiliumWatcher(cfg)
			assert.NoError(t, err)
			assert.NotNil(t, watcher)
			assert.Equal(t, tt.expectedEndpoint, watcher.hubbleEndpoint)
		})
	}
}

type TestCiliumWatcher struct {
	*CiliumWatcher
	connectFunc func() error
}

func (w *TestCiliumWatcher) connectToHubble() error {
	if w.connectFunc != nil {
		return w.connectFunc()
	}
	return w.CiliumWatcher.connectToHubble()
}

func TestCiliumWatcher_ConnectToHubble(t *testing.T) {
	clientset := fake.NewSimpleClientset()
	log := logrus.New()
	otelService := otel.NewOpenTelemetryService(context.Background(), log)

	tests := []struct {
		name           string
		endpoint       string
		expectedError  bool
		expectedClient bool
	}{
		{
			name:           "Valid endpoint",
			endpoint:       "localhost:4244",
			expectedError:  false,
			expectedClient: true,
		},
		{
			name:           "Invalid endpoint",
			endpoint:       "invalid-endpoint",
			expectedError:  true,
			expectedClient: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			watcher := &CiliumWatcher{
				CNIWatcherConfig: CNIWatcherConfig{
					Clientset:   clientset,
					NodeName:    "test-node",
					Log:         log,
					OtelService: otelService,
				},
				hubbleEndpoint: tt.endpoint,
			}

			testWatcher := &TestCiliumWatcher{
				CiliumWatcher: watcher,
				connectFunc: func() error {
					if tt.expectedError {
						return fmt.Errorf("connection failed")
					}
					watcher.client = &MockObserverClient{}
					return nil
				},
			}

			err := testWatcher.connectToHubble()

			if tt.expectedError {
				assert.Error(t, err)
				assert.Nil(t, testWatcher.client)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, testWatcher.client)
			}
		})
	}
}

type MockObserverClient struct {
	hubbleObserver.ObserverClient
	getFlowsFunc func(ctx context.Context, req *hubbleObserver.GetFlowsRequest, opts ...grpc.CallOption) (hubbleObserver.Observer_GetFlowsClient, error)
}

func (m *MockObserverClient) GetFlows(ctx context.Context, req *hubbleObserver.GetFlowsRequest, opts ...grpc.CallOption) (hubbleObserver.Observer_GetFlowsClient, error) {
	if m.getFlowsFunc != nil {
		return m.getFlowsFunc(ctx, req, opts...)
	}
	return nil, fmt.Errorf("getFlows function not set")
}

type MockGetFlowsClient struct {
	hubbleObserver.Observer_GetFlowsClient
	recvFunc func() (*hubbleObserver.GetFlowsResponse, error)
}

func (m *MockGetFlowsClient) Recv() (*hubbleObserver.GetFlowsResponse, error) {
	if m.recvFunc != nil {
		return m.recvFunc()
	}
	return nil, fmt.Errorf("recv function not set")
}

func TestCiliumWatcher_WatchFlows(t *testing.T) {
	clientset := fake.NewSimpleClientset()
	log := logrus.New()
	otelService := otel.NewOpenTelemetryService(context.Background(), log)

	watcher := &CiliumWatcher{
		CNIWatcherConfig: CNIWatcherConfig{
			Clientset:   clientset,
			NodeName:    "test-node",
			Log:         log,
			OtelService: otelService,
		},
		hubbleEndpoint: "localhost:4244",
	}

	tests := []struct {
		name          string
		getFlowsFunc  func(ctx context.Context, req *hubbleObserver.GetFlowsRequest, opts ...grpc.CallOption) (hubbleObserver.Observer_GetFlowsClient, error)
		recvFunc      func() (*hubbleObserver.GetFlowsResponse, error)
		expectedError bool
	}{
		{
			name: "Successful flow stream",
			getFlowsFunc: func(ctx context.Context, req *hubbleObserver.GetFlowsRequest, opts ...grpc.CallOption) (hubbleObserver.Observer_GetFlowsClient, error) {
				return &MockGetFlowsClient{
					recvFunc: func() (*hubbleObserver.GetFlowsResponse, error) {
						return &hubbleObserver.GetFlowsResponse{
							ResponseTypes: &hubbleObserver.GetFlowsResponse_Flow{
								Flow: &flowpb.Flow{
									EventType: &flowpb.CiliumEventType{
										Type: monitorApi.MessageTypePolicyVerdict,
									},
									DropReasonDesc: flowpb.DropReason_POLICY_DENIED,
								},
							},
						}, nil
					},
				}, nil
			},
			recvFunc: func() (*hubbleObserver.GetFlowsResponse, error) {
				return &hubbleObserver.GetFlowsResponse{
					ResponseTypes: &hubbleObserver.GetFlowsResponse_Flow{
						Flow: &flowpb.Flow{
							EventType: &flowpb.CiliumEventType{
								Type: monitorApi.MessageTypePolicyVerdict,
							},
							DropReasonDesc: flowpb.DropReason_POLICY_DENIED,
						},
					},
				}, nil
			},
			expectedError: false,
		},
		{
			name: "GetFlows error",
			getFlowsFunc: func(ctx context.Context, req *hubbleObserver.GetFlowsRequest, opts ...grpc.CallOption) (hubbleObserver.Observer_GetFlowsClient, error) {
				return nil, fmt.Errorf("getFlows error")
			},
			recvFunc:      nil,
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			watcher.client = &MockObserverClient{
				getFlowsFunc: tt.getFlowsFunc,
			}

			ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
			defer cancel()
			watcher.Ctx = ctx

			err := watcher.watchFlows()
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				// watchFlows will likely timeout due to context cancellation, which is expected
				assert.NoError(t, err)
			}
		})
	}
}

func TestCiliumWatcher_ParsePolicyDenyEvent(t *testing.T) {
	tests := []struct {
		name          string
		flow          *hubbleObserver.GetFlowsResponse
		expectedError bool
	}{
		{
			name: "Valid policy deny flow",
			flow: &hubbleObserver.GetFlowsResponse{
				ResponseTypes: &hubbleObserver.GetFlowsResponse_Flow{
					Flow: &flowpb.Flow{
						EventType: &flowpb.CiliumEventType{
							Type: monitorApi.MessageTypePolicyVerdict,
						},
						DropReasonDesc: flowpb.DropReason_POLICY_DENIED,
					},
				},
			},
			expectedError: false,
		},
		{
			name: "Allow flow (should be ignored)",
			flow: &hubbleObserver.GetFlowsResponse{
				ResponseTypes: &hubbleObserver.GetFlowsResponse_Flow{
					Flow: &flowpb.Flow{
						EventType: &flowpb.CiliumEventType{
							Type: monitorApi.MessageTypePolicyVerdict,
						},
						DropReasonDesc: flowpb.DropReason_POLICY_DENY,
					},
				},
			},
			expectedError: false,
		},
		{
			name:          "Nil flow",
			flow:          nil,
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			log := logrus.New()
			otelService := otel.NewOpenTelemetryService(context.Background(), log)

			watcher := &CiliumWatcher{
				CNIWatcherConfig: CNIWatcherConfig{
					Log:         log,
					OtelService: otelService,
				},
			}

			err := watcher.parsePolicyDenyEvent(tt.flow)
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestCiliumWatcher_Shutdown(t *testing.T) {
	tests := []struct {
		name    string
		wantErr bool
	}{
		{
			name:    "Successful shutdown",
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			log := logrus.New()
			otelService := otel.NewOpenTelemetryService(context.Background(), log)

			watcher := &CiliumWatcher{
				CNIWatcherConfig: CNIWatcherConfig{
					Log:         log,
					OtelService: otelService,
				},
			}

			err := watcher.Shutdown()
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
