package cniwatcher

import (
	"context"
	"testing"
	"time"

	flowpb "github.com/cilium/cilium/api/v1/flow"
	hubbleObserver "github.com/cilium/cilium/api/v1/observer"
	monitorApi "github.com/cilium/cilium/pkg/monitor/api"
	"github.com/go-logr/logr"
	"github.com/stretchr/testify/assert"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes/fake"
	"k8s.io/client-go/rest"
)

func TestNewCiliumWatcher(t *testing.T) {
	tests := []struct {
		name     string
		nodeName string
		wantErr  bool
	}{
		{
			name:     "Valid node name",
			nodeName: "test-node",
			wantErr:  false,
		},
		{
			name:     "Empty node name",
			nodeName: "",
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fakeClient := fake.NewSimpleClientset()
			config := &rest.Config{}
			log := logr.Discard()

			watcher, err := NewCiliumWatcher(fakeClient, config, log, tt.nodeName)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, watcher)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, watcher)
				assert.Equal(t, CNITypeCilium, watcher.cniType)
			}
		})
	}
}

func TestCiliumWatcher_Start(t *testing.T) {
	tests := []struct {
		name    string
		wantErr bool
	}{
		{
			name:    "Successful start",
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fakeClient := fake.NewSimpleClientset()
			watcher := &CiliumWatcher{
				BaseWatcher: BaseWatcher{
					clientset: fakeClient,
					log:       logr.Discard(),
					nodeName:  "test-node",
					cniType:   CNITypeCilium,
				},
			}

			ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
			defer cancel()

			err := watcher.Start(ctx)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestCiliumWatcher_ParsePolicyDenyEvent(t *testing.T) {
	tests := []struct {
		name    string
		flow    *hubbleObserver.GetFlowsResponse
		wantErr bool
	}{
		{
			name: "Valid flow data",
			flow: &hubbleObserver.GetFlowsResponse{
				ResponseTypes: &hubbleObserver.GetFlowsResponse_Flow{
					Flow: &flowpb.Flow{
						EventType: &flowpb.CiliumEventType{
							Type: monitorApi.MessageTypePolicyVerdict,
						},
						DropReasonDesc: flowpb.DropReason_POLICY_DENIED,
						Source: &flowpb.Endpoint{
							Namespace: "default",
							PodName:   "test-pod",
							Labels:    []string{"k8s:app=test"},
						},
						Destination: &flowpb.Endpoint{
							Namespace: "default",
							PodName:   "web-pod",
							Labels:    []string{"k8s:app=web"},
						},
						L4: &flowpb.Layer4{
							Protocol: &flowpb.Layer4_TCP{
								TCP: &flowpb.TCP{
									SourcePort:      12345,
									DestinationPort: 80,
								},
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name:    "Nil flow",
			flow:    nil,
			wantErr: true,
		},
		{
			name: "Invalid flow type",
			flow: &hubbleObserver.GetFlowsResponse{
				ResponseTypes: &hubbleObserver.GetFlowsResponse_Flow{
					Flow: nil,
				},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			watcher := &CiliumWatcher{
				BaseWatcher: BaseWatcher{
					log: logr.Discard(),
				},
			}

			err := watcher.parsePolicyDenyEvent(tt.flow)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestCiliumWatcher_GetEndpointInfoByIP(t *testing.T) {
	tests := []struct {
		name    string
		ip      string
		pods    []*corev1.Pod
		wantErr bool
	}{
		{
			name: "Valid IP address",
			ip:   "********",
			pods: []*corev1.Pod{
				{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "test-pod",
						Namespace: "default",
						Labels: map[string]string{
							"app": "test",
						},
					},
					Status: corev1.PodStatus{
						PodIP: "********",
					},
				},
			},
			wantErr: false,
		},
		{
			name:    "Invalid IP address",
			ip:      "invalid-ip",
			wantErr: true,
		},
		{
			name:    "Non-existent IP",
			ip:      "*********",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fakeClient := fake.NewSimpleClientset()
			for _, pod := range tt.pods {
				_, err := fakeClient.CoreV1().Pods(pod.Namespace).Create(context.Background(), pod, metav1.CreateOptions{})
				assert.NoError(t, err)
			}

			watcher := &CiliumWatcher{
				BaseWatcher: BaseWatcher{
					clientset: fakeClient,
					log:       logr.Discard(),
				},
			}

			info, err := watcher.getEndpointInfoByIP(tt.ip)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, info)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, info)
				assert.Equal(t, tt.pods[0].Name, info.Name)
				assert.Equal(t, tt.pods[0].Namespace, info.Namespace)
			}
		})
	}
}

func TestCiliumWatcher_Shutdown(t *testing.T) {
	tests := []struct {
		name    string
		wantErr bool
	}{
		{
			name:    "Successful shutdown",
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fakeClient := fake.NewSimpleClientset()
			watcher := &CiliumWatcher{
				BaseWatcher: BaseWatcher{
					clientset: fakeClient,
					log:       logr.Discard(),
				},
			}

			ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
			defer cancel()

			err := watcher.Shutdown(ctx)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
