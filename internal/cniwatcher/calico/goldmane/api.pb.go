// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: internal/cniwatcher/calico/goldmane/api.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// FilterType specifies which fields on the underlying Flow data to collect.
type FilterType int32

const (
	FilterType_FilterTypeUnspecified     FilterType = 0
	FilterType_FilterTypeDestName        FilterType = 1
	FilterType_FilterTypeSourceName      FilterType = 2
	FilterType_FilterTypeDestNamespace   FilterType = 3
	FilterType_FilterTypeSourceNamespace FilterType = 4
	FilterType_FilterTypePolicyTier      FilterType = 5
	FilterType_FilterTypePolicyName      FilterType = 6
)

// Enum value maps for FilterType.
var (
	FilterType_name = map[int32]string{
		0: "FilterTypeUnspecified",
		1: "FilterTypeDestName",
		2: "FilterTypeSourceName",
		3: "FilterTypeDestNamespace",
		4: "FilterTypeSourceNamespace",
		5: "FilterTypePolicyTier",
		6: "FilterTypePolicyName",
	}
	FilterType_value = map[string]int32{
		"FilterTypeUnspecified":     0,
		"FilterTypeDestName":        1,
		"FilterTypeSourceName":      2,
		"FilterTypeDestNamespace":   3,
		"FilterTypeSourceNamespace": 4,
		"FilterTypePolicyTier":      5,
		"FilterTypePolicyName":      6,
	}
)

func (x FilterType) Enum() *FilterType {
	p := new(FilterType)
	*p = x
	return p
}

func (x FilterType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FilterType) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_cniwatcher_calico_goldmane_api_proto_enumTypes[0].Descriptor()
}

func (FilterType) Type() protoreflect.EnumType {
	return &file_internal_cniwatcher_calico_goldmane_api_proto_enumTypes[0]
}

func (x FilterType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FilterType.Descriptor instead.
func (FilterType) EnumDescriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{0}
}

type Action int32

const (
	Action_ActionUnspecified Action = 0
	Action_Allow             Action = 1
	Action_Deny              Action = 2
	Action_Pass              Action = 3
)

// Enum value maps for Action.
var (
	Action_name = map[int32]string{
		0: "ActionUnspecified",
		1: "Allow",
		2: "Deny",
		3: "Pass",
	}
	Action_value = map[string]int32{
		"ActionUnspecified": 0,
		"Allow":             1,
		"Deny":              2,
		"Pass":              3,
	}
)

func (x Action) Enum() *Action {
	p := new(Action)
	*p = x
	return p
}

func (x Action) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Action) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_cniwatcher_calico_goldmane_api_proto_enumTypes[1].Descriptor()
}

func (Action) Type() protoreflect.EnumType {
	return &file_internal_cniwatcher_calico_goldmane_api_proto_enumTypes[1]
}

func (x Action) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Action.Descriptor instead.
func (Action) EnumDescriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{1}
}

type MatchType int32

const (
	// Match the value exactly.
	MatchType_Exact MatchType = 0
	// Use fuzzy matching on the value.
	MatchType_Fuzzy MatchType = 1
)

// Enum value maps for MatchType.
var (
	MatchType_name = map[int32]string{
		0: "Exact",
		1: "Fuzzy",
	}
	MatchType_value = map[string]int32{
		"Exact": 0,
		"Fuzzy": 1,
	}
)

func (x MatchType) Enum() *MatchType {
	p := new(MatchType)
	*p = x
	return p
}

func (x MatchType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MatchType) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_cniwatcher_calico_goldmane_api_proto_enumTypes[2].Descriptor()
}

func (MatchType) Type() protoreflect.EnumType {
	return &file_internal_cniwatcher_calico_goldmane_api_proto_enumTypes[2]
}

func (x MatchType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MatchType.Descriptor instead.
func (MatchType) EnumDescriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{2}
}

type PolicyKind int32

const (
	// Unspecified
	PolicyKind_KindUnspecified PolicyKind = 0
	// Calico policy types.
	PolicyKind_CalicoNetworkPolicy           PolicyKind = 1
	PolicyKind_GlobalNetworkPolicy           PolicyKind = 2
	PolicyKind_StagedNetworkPolicy           PolicyKind = 3
	PolicyKind_StagedGlobalNetworkPolicy     PolicyKind = 4
	PolicyKind_StagedKubernetesNetworkPolicy PolicyKind = 5
	// Native Kubernetes types.
	PolicyKind_NetworkPolicy              PolicyKind = 6
	PolicyKind_AdminNetworkPolicy         PolicyKind = 7
	PolicyKind_BaselineAdminNetworkPolicy PolicyKind = 8
	// Calico Profiles.
	PolicyKind_Profile   PolicyKind = 9
	PolicyKind_EndOfTier PolicyKind = 10
)

// Enum value maps for PolicyKind.
var (
	PolicyKind_name = map[int32]string{
		0:  "KindUnspecified",
		1:  "CalicoNetworkPolicy",
		2:  "GlobalNetworkPolicy",
		3:  "StagedNetworkPolicy",
		4:  "StagedGlobalNetworkPolicy",
		5:  "StagedKubernetesNetworkPolicy",
		6:  "NetworkPolicy",
		7:  "AdminNetworkPolicy",
		8:  "BaselineAdminNetworkPolicy",
		9:  "Profile",
		10: "EndOfTier",
	}
	PolicyKind_value = map[string]int32{
		"KindUnspecified":               0,
		"CalicoNetworkPolicy":           1,
		"GlobalNetworkPolicy":           2,
		"StagedNetworkPolicy":           3,
		"StagedGlobalNetworkPolicy":     4,
		"StagedKubernetesNetworkPolicy": 5,
		"NetworkPolicy":                 6,
		"AdminNetworkPolicy":            7,
		"BaselineAdminNetworkPolicy":    8,
		"Profile":                       9,
		"EndOfTier":                     10,
	}
)

func (x PolicyKind) Enum() *PolicyKind {
	p := new(PolicyKind)
	*p = x
	return p
}

func (x PolicyKind) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PolicyKind) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_cniwatcher_calico_goldmane_api_proto_enumTypes[3].Descriptor()
}

func (PolicyKind) Type() protoreflect.EnumType {
	return &file_internal_cniwatcher_calico_goldmane_api_proto_enumTypes[3]
}

func (x PolicyKind) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PolicyKind.Descriptor instead.
func (PolicyKind) EnumDescriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{3}
}

type SortBy int32

const (
	SortBy_Time            SortBy = 0
	SortBy_DestName        SortBy = 1
	SortBy_DestNamespace   SortBy = 2
	SortBy_DestType        SortBy = 3
	SortBy_SourceName      SortBy = 4
	SortBy_SourceNamespace SortBy = 5
	SortBy_SourceType      SortBy = 6
)

// Enum value maps for SortBy.
var (
	SortBy_name = map[int32]string{
		0: "Time",
		1: "DestName",
		2: "DestNamespace",
		3: "DestType",
		4: "SourceName",
		5: "SourceNamespace",
		6: "SourceType",
	}
	SortBy_value = map[string]int32{
		"Time":            0,
		"DestName":        1,
		"DestNamespace":   2,
		"DestType":        3,
		"SourceName":      4,
		"SourceNamespace": 5,
		"SourceType":      6,
	}
)

func (x SortBy) Enum() *SortBy {
	p := new(SortBy)
	*p = x
	return p
}

func (x SortBy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SortBy) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_cniwatcher_calico_goldmane_api_proto_enumTypes[4].Descriptor()
}

func (SortBy) Type() protoreflect.EnumType {
	return &file_internal_cniwatcher_calico_goldmane_api_proto_enumTypes[4]
}

func (x SortBy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SortBy.Descriptor instead.
func (SortBy) EnumDescriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{4}
}

type EndpointType int32

const (
	// For queries, unspecified means "do not filter on this field".
	EndpointType_EndpointTypeUnspecified EndpointType = 0
	// WorkloadEndpoint represents an application endpoint with its own network identity. For example,
	// a Kubernetes Pod.
	EndpointType_WorkloadEndpoint EndpointType = 1
	// HostEndpoint represents a host machine.
	EndpointType_HostEndpoint EndpointType = 2
	// NetworkSet represents an address from within a configured projectcalico.org/v3 NetworkSet or
	// GlobalNetworkSet.
	EndpointType_NetworkSet EndpointType = 3
	// Network represents an endpoint on a public or private network not known by Calico. For example,
	// traffic from the public internet or private LAN not covered by a NetworkSet.
	EndpointType_Network EndpointType = 4
)

// Enum value maps for EndpointType.
var (
	EndpointType_name = map[int32]string{
		0: "EndpointTypeUnspecified",
		1: "WorkloadEndpoint",
		2: "HostEndpoint",
		3: "NetworkSet",
		4: "Network",
	}
	EndpointType_value = map[string]int32{
		"EndpointTypeUnspecified": 0,
		"WorkloadEndpoint":        1,
		"HostEndpoint":            2,
		"NetworkSet":              3,
		"Network":                 4,
	}
)

func (x EndpointType) Enum() *EndpointType {
	p := new(EndpointType)
	*p = x
	return p
}

func (x EndpointType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EndpointType) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_cniwatcher_calico_goldmane_api_proto_enumTypes[5].Descriptor()
}

func (EndpointType) Type() protoreflect.EnumType {
	return &file_internal_cniwatcher_calico_goldmane_api_proto_enumTypes[5]
}

func (x EndpointType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EndpointType.Descriptor instead.
func (EndpointType) EnumDescriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{5}
}

type Reporter int32

const (
	// For queries, unspecified means "do not filter on this field".
	Reporter_ReporterUnspecified Reporter = 0
	Reporter_Src                 Reporter = 1
	Reporter_Dst                 Reporter = 2
)

// Enum value maps for Reporter.
var (
	Reporter_name = map[int32]string{
		0: "ReporterUnspecified",
		1: "Src",
		2: "Dst",
	}
	Reporter_value = map[string]int32{
		"ReporterUnspecified": 0,
		"Src":                 1,
		"Dst":                 2,
	}
)

func (x Reporter) Enum() *Reporter {
	p := new(Reporter)
	*p = x
	return p
}

func (x Reporter) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Reporter) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_cniwatcher_calico_goldmane_api_proto_enumTypes[6].Descriptor()
}

func (Reporter) Type() protoreflect.EnumType {
	return &file_internal_cniwatcher_calico_goldmane_api_proto_enumTypes[6]
}

func (x Reporter) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Reporter.Descriptor instead.
func (Reporter) EnumDescriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{6}
}

// StatisticType represents the types of data available over the Statistics API endpoint.
type StatisticType int32

const (
	StatisticType_PacketCount         StatisticType = 0
	StatisticType_ByteCount           StatisticType = 1
	StatisticType_LiveConnectionCount StatisticType = 2
)

// Enum value maps for StatisticType.
var (
	StatisticType_name = map[int32]string{
		0: "PacketCount",
		1: "ByteCount",
		2: "LiveConnectionCount",
	}
	StatisticType_value = map[string]int32{
		"PacketCount":         0,
		"ByteCount":           1,
		"LiveConnectionCount": 2,
	}
)

func (x StatisticType) Enum() *StatisticType {
	p := new(StatisticType)
	*p = x
	return p
}

func (x StatisticType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StatisticType) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_cniwatcher_calico_goldmane_api_proto_enumTypes[7].Descriptor()
}

func (StatisticType) Type() protoreflect.EnumType {
	return &file_internal_cniwatcher_calico_goldmane_api_proto_enumTypes[7]
}

func (x StatisticType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StatisticType.Descriptor instead.
func (StatisticType) EnumDescriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{7}
}

type StatisticsGroupBy int32

const (
	// Policy configures statistics groupings on a per-policy basis.
	StatisticsGroupBy_Policy StatisticsGroupBy = 0
	// PolicyRule configures statistics groupings on a per-policy-rule basis.
	StatisticsGroupBy_PolicyRule StatisticsGroupBy = 1
)

// Enum value maps for StatisticsGroupBy.
var (
	StatisticsGroupBy_name = map[int32]string{
		0: "Policy",
		1: "PolicyRule",
	}
	StatisticsGroupBy_value = map[string]int32{
		"Policy":     0,
		"PolicyRule": 1,
	}
)

func (x StatisticsGroupBy) Enum() *StatisticsGroupBy {
	p := new(StatisticsGroupBy)
	*p = x
	return p
}

func (x StatisticsGroupBy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StatisticsGroupBy) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_cniwatcher_calico_goldmane_api_proto_enumTypes[8].Descriptor()
}

func (StatisticsGroupBy) Type() protoreflect.EnumType {
	return &file_internal_cniwatcher_calico_goldmane_api_proto_enumTypes[8]
}

func (x StatisticsGroupBy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StatisticsGroupBy.Descriptor instead.
func (StatisticsGroupBy) EnumDescriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{8}
}

type RuleDirection int32

const (
	RuleDirection_Any     RuleDirection = 0
	RuleDirection_Ingress RuleDirection = 1
	RuleDirection_Egress  RuleDirection = 2
)

// Enum value maps for RuleDirection.
var (
	RuleDirection_name = map[int32]string{
		0: "Any",
		1: "Ingress",
		2: "Egress",
	}
	RuleDirection_value = map[string]int32{
		"Any":     0,
		"Ingress": 1,
		"Egress":  2,
	}
)

func (x RuleDirection) Enum() *RuleDirection {
	p := new(RuleDirection)
	*p = x
	return p
}

func (x RuleDirection) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RuleDirection) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_cniwatcher_calico_goldmane_api_proto_enumTypes[9].Descriptor()
}

func (RuleDirection) Type() protoreflect.EnumType {
	return &file_internal_cniwatcher_calico_goldmane_api_proto_enumTypes[9]
}

func (x RuleDirection) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RuleDirection.Descriptor instead.
func (RuleDirection) EnumDescriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{9}
}

// FlowListRequest defines a message to request a particular selection of aggregated Flow objects.
type FlowListRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// StartTimeGt specifies the beginning of a time window with which to filter Flows. Flows
	// will be returned only if their start time is greater than or equal to the given value.
	//
	// - A value of zero indicates the oldest start time available by the server.
	// - A value greater than zero indicates an absolute time in seconds since the Unix epoch.
	// - A value less than zero indicates a relative number of seconds from "now", as determined by the server.
	StartTimeGte int64 `protobuf:"varint,1,opt,name=start_time_gte,json=startTimeGte,proto3" json:"start_time_gte,omitempty"`
	// StartTimeLt specifies the end of a time window with which to filter flows. Flows will
	// be returned only if their start time occurs before the requested time.
	//
	// - A value of zero means "now", as determined by the server at the time of request.
	// - A value greater than zero indicates an absolute time in seconds since the Unix epoch.
	// - A value less than zero indicates a relative number of seconds from "now", as determined by the server.
	StartTimeLt int64 `protobuf:"varint,2,opt,name=start_time_lt,json=startTimeLt,proto3" json:"start_time_lt,omitempty"`
	// Page specifies the page to return. It requires that PageSize is also specified in order
	// to determine page boundaries. Note that pages may change over time as new flow data is collected or expired.
	// Querying the same page at different points in time may return different results.
	Page int64 `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	// PageSize configures the maximum number of results to return as part of this query.
	PageSize int64 `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// SortBy configures how to sort the results of this query. By default flows are sorted by start time.
	// The returned list is sorted by each sort option, in order, using the next sort option in the list as a tie-breaker.
	// Note: At the moment, only a single sort option is supported.
	SortBy []*SortOption `protobuf:"bytes,5,rep,name=sort_by,json=sortBy,proto3" json:"sort_by,omitempty"`
	// Filter allows specification of one or more criteria on which to filter the returned Flows.
	Filter *Filter `protobuf:"bytes,6,opt,name=filter,proto3" json:"filter,omitempty"`
	// AggregationInterval is the width of the time window in seconds across which to aggregate when generating
	// Flows to return. This must be a multiple of 15.
	AggregationInterval int64 `protobuf:"varint,7,opt,name=aggregation_interval,json=aggregationInterval,proto3" json:"aggregation_interval,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *FlowListRequest) Reset() {
	*x = FlowListRequest{}
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FlowListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlowListRequest) ProtoMessage() {}

func (x *FlowListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlowListRequest.ProtoReflect.Descriptor instead.
func (*FlowListRequest) Descriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{0}
}

func (x *FlowListRequest) GetStartTimeGte() int64 {
	if x != nil {
		return x.StartTimeGte
	}
	return 0
}

func (x *FlowListRequest) GetStartTimeLt() int64 {
	if x != nil {
		return x.StartTimeLt
	}
	return 0
}

func (x *FlowListRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *FlowListRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *FlowListRequest) GetSortBy() []*SortOption {
	if x != nil {
		return x.SortBy
	}
	return nil
}

func (x *FlowListRequest) GetFilter() *Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *FlowListRequest) GetAggregationInterval() int64 {
	if x != nil {
		return x.AggregationInterval
	}
	return 0
}

// FlowListResult is a message containing a list of FlowResults and ListMetadata.
type FlowListResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Meta specifies metadata about the returned flows.
	Meta *ListMetadata `protobuf:"bytes,1,opt,name=meta,proto3" json:"meta,omitempty"`
	// Flows is a list of FlowResult objects.
	Flows         []*FlowResult `protobuf:"bytes,2,rep,name=flows,proto3" json:"flows,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FlowListResult) Reset() {
	*x = FlowListResult{}
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FlowListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlowListResult) ProtoMessage() {}

func (x *FlowListResult) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlowListResult.ProtoReflect.Descriptor instead.
func (*FlowListResult) Descriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{1}
}

func (x *FlowListResult) GetMeta() *ListMetadata {
	if x != nil {
		return x.Meta
	}
	return nil
}

func (x *FlowListResult) GetFlows() []*FlowResult {
	if x != nil {
		return x.Flows
	}
	return nil
}

// FlowStreamRequest defines a message to request a stream of aggregated Flows.
type FlowStreamRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// StartTimeGt specifies the beginning of a time window from which to stream Flows. Flows
	// will be streamed only if their start time is greater than or equal to the given value.
	//
	// - A value of zero means "now", as determined by the server at the time of request.
	// - A value greater than zero indicates an absolute time in seconds since the Unix epoch.
	// - A value less than zero indicates a relative number of seconds from "now", as determined by the server.
	StartTimeGte int64 `protobuf:"varint,1,opt,name=start_time_gte,json=startTimeGte,proto3" json:"start_time_gte,omitempty"`
	// Filter allows specification of one or more criteria on which to filter the returned Flows.
	Filter *Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
	// AggregationInterval defines both the frequency of streamed updates for each Flow, and the amount of time that FlowResult covers.
	// It must always be 15s.
	//
	// Every AggregationInterval the server must send a FlowResult containing the aggregated data for that Flow from a
	// time interval of width AggregationInterval.
	//
	// For a Flow that has continuous traffic, the server should send updates covering the range
	// [now-2*AggregationInterval, now-AggregationInterval] so that the data is reasonably likely to be complete.
	AggregationInterval int64 `protobuf:"varint,3,opt,name=aggregation_interval,json=aggregationInterval,proto3" json:"aggregation_interval,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *FlowStreamRequest) Reset() {
	*x = FlowStreamRequest{}
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FlowStreamRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlowStreamRequest) ProtoMessage() {}

func (x *FlowStreamRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlowStreamRequest.ProtoReflect.Descriptor instead.
func (*FlowStreamRequest) Descriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{2}
}

func (x *FlowStreamRequest) GetStartTimeGte() int64 {
	if x != nil {
		return x.StartTimeGte
	}
	return 0
}

func (x *FlowStreamRequest) GetFilter() *Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *FlowStreamRequest) GetAggregationInterval() int64 {
	if x != nil {
		return x.AggregationInterval
	}
	return 0
}

type FilterHintsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Type is type of Filter to query.
	Type FilterType `protobuf:"varint,1,opt,name=type,proto3,enum=goldmane.FilterType" json:"type,omitempty"`
	// Filter is a set of filter criteria used to narrow down returned results.
	Filter *Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
	// StartTimeGt specifies the beginning of a time window with which to filter (inclusive).
	//
	// - A value of zero indicates the oldest start time available by the server.
	// - A value greater than zero indicates an absolute time in seconds since the Unix epoch.
	// - A value less than zero indicates a relative number of seconds from "now", as determined by the server.
	StartTimeGte int64 `protobuf:"varint,3,opt,name=start_time_gte,json=startTimeGte,proto3" json:"start_time_gte,omitempty"`
	// StartTimeLt specifies the end of a time window with which to filter.
	//
	// - A value of zero means "now", as determined by the server at the time of request.
	// - A value greater than zero indicates an absolute time in seconds since the Unix epoch.
	// - A value less than zero indicates a relative number of seconds from "now", as determined by the server.
	StartTimeLt int64 `protobuf:"varint,4,opt,name=start_time_lt,json=startTimeLt,proto3" json:"start_time_lt,omitempty"`
	// Page specifies the page number to return. It requires that PageSize is also specified in order
	// to determine page boundaries. Note that pages may change over time as new flow data is collected or expired.
	// Querying the same page at different points in time may return different results.
	Page int64 `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	// PageSize configures the maximum number of results to return as part of this query.
	PageSize      int64 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterHintsRequest) Reset() {
	*x = FilterHintsRequest{}
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterHintsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterHintsRequest) ProtoMessage() {}

func (x *FilterHintsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterHintsRequest.ProtoReflect.Descriptor instead.
func (*FilterHintsRequest) Descriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{3}
}

func (x *FilterHintsRequest) GetType() FilterType {
	if x != nil {
		return x.Type
	}
	return FilterType_FilterTypeUnspecified
}

func (x *FilterHintsRequest) GetFilter() *Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *FilterHintsRequest) GetStartTimeGte() int64 {
	if x != nil {
		return x.StartTimeGte
	}
	return 0
}

func (x *FilterHintsRequest) GetStartTimeLt() int64 {
	if x != nil {
		return x.StartTimeLt
	}
	return 0
}

func (x *FilterHintsRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *FilterHintsRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type FilterHintsResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ListMetadata specifies list information about the flows returned.
	Meta *ListMetadata `protobuf:"bytes,1,opt,name=meta,proto3" json:"meta,omitempty"`
	// FilterHint contains the values that flows can be filtered on.
	Hints         []*FilterHint `protobuf:"bytes,2,rep,name=hints,proto3" json:"hints,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterHintsResult) Reset() {
	*x = FilterHintsResult{}
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterHintsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterHintsResult) ProtoMessage() {}

func (x *FilterHintsResult) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterHintsResult.ProtoReflect.Descriptor instead.
func (*FilterHintsResult) Descriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{4}
}

func (x *FilterHintsResult) GetMeta() *ListMetadata {
	if x != nil {
		return x.Meta
	}
	return nil
}

func (x *FilterHintsResult) GetHints() []*FilterHint {
	if x != nil {
		return x.Hints
	}
	return nil
}

// ListMetadata contains information about a returned list of items, such as pagination information (total number of pages
// and total number of results).
type ListMetadata struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// totalPages is the total number of pages that exist given that a pageSize was specified.
	TotalPages int64 `protobuf:"varint,1,opt,name=totalPages,proto3" json:"totalPages,omitempty"`
	// TotalResults are the total number of results that would have been returned if no pagination was specified.
	TotalResults  int64 `protobuf:"varint,2,opt,name=totalResults,proto3" json:"totalResults,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMetadata) Reset() {
	*x = ListMetadata{}
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMetadata) ProtoMessage() {}

func (x *ListMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMetadata.ProtoReflect.Descriptor instead.
func (*ListMetadata) Descriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{5}
}

func (x *ListMetadata) GetTotalPages() int64 {
	if x != nil {
		return x.TotalPages
	}
	return 0
}

func (x *ListMetadata) GetTotalResults() int64 {
	if x != nil {
		return x.TotalResults
	}
	return 0
}

type FilterHint struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Value         string                 `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterHint) Reset() {
	*x = FilterHint{}
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterHint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterHint) ProtoMessage() {}

func (x *FilterHint) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterHint.ProtoReflect.Descriptor instead.
func (*FilterHint) Descriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{6}
}

func (x *FilterHint) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// FlowResult wraps a Flow object with additional metadata.
type FlowResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ID is an opaque integer value ID that can be used to identify a Flow, and is 1:1 with the FlowKey.
	// Note that this ID is not valid across server restarts. Its primary use-case is for correlating FlowResult
	// updates from a Stream request.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The Flow object itself.
	Flow          *Flow `protobuf:"bytes,2,opt,name=flow,proto3" json:"flow,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FlowResult) Reset() {
	*x = FlowResult{}
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FlowResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlowResult) ProtoMessage() {}

func (x *FlowResult) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlowResult.ProtoReflect.Descriptor instead.
func (*FlowResult) Descriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{7}
}

func (x *FlowResult) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FlowResult) GetFlow() *Flow {
	if x != nil {
		return x.Flow
	}
	return nil
}

// Filter defines criteria for selecting a set of Flows based on their parameters.
type Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// SourceNames allows filtering on the source name field. Combined using logical OR.
	SourceNames []*StringMatch `protobuf:"bytes,1,rep,name=source_names,json=sourceNames,proto3" json:"source_names,omitempty"`
	// SourceNamespaces filters on the source namespace field. Combined using logical OR.
	SourceNamespaces []*StringMatch `protobuf:"bytes,2,rep,name=source_namespaces,json=sourceNamespaces,proto3" json:"source_namespaces,omitempty"`
	// DestNames filters on the destination name field. Combined using logical OR.
	DestNames []*StringMatch `protobuf:"bytes,3,rep,name=dest_names,json=destNames,proto3" json:"dest_names,omitempty"`
	// DestNamespaces filters on the destination namespace field. Combined using logical OR.
	DestNamespaces []*StringMatch `protobuf:"bytes,4,rep,name=dest_namespaces,json=destNamespaces,proto3" json:"dest_namespaces,omitempty"`
	// Protocols filters on the protocol field. Combined using logical OR.
	Protocols []*StringMatch `protobuf:"bytes,5,rep,name=protocols,proto3" json:"protocols,omitempty"`
	// DestPorts filters on the port field. Combined using logical OR.
	DestPorts []*PortMatch `protobuf:"bytes,6,rep,name=dest_ports,json=destPorts,proto3" json:"dest_ports,omitempty"`
	// Actions filters on the action field. Combined using logical OR.
	Actions []Action `protobuf:"varint,7,rep,packed,name=actions,proto3,enum=goldmane.Action" json:"actions,omitempty"`
	// Policies matches on policy fields. Combined using logical OR.
	Policies      []*PolicyMatch `protobuf:"bytes,8,rep,name=policies,proto3" json:"policies,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Filter) Reset() {
	*x = Filter{}
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Filter) ProtoMessage() {}

func (x *Filter) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Filter.ProtoReflect.Descriptor instead.
func (*Filter) Descriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{8}
}

func (x *Filter) GetSourceNames() []*StringMatch {
	if x != nil {
		return x.SourceNames
	}
	return nil
}

func (x *Filter) GetSourceNamespaces() []*StringMatch {
	if x != nil {
		return x.SourceNamespaces
	}
	return nil
}

func (x *Filter) GetDestNames() []*StringMatch {
	if x != nil {
		return x.DestNames
	}
	return nil
}

func (x *Filter) GetDestNamespaces() []*StringMatch {
	if x != nil {
		return x.DestNamespaces
	}
	return nil
}

func (x *Filter) GetProtocols() []*StringMatch {
	if x != nil {
		return x.Protocols
	}
	return nil
}

func (x *Filter) GetDestPorts() []*PortMatch {
	if x != nil {
		return x.DestPorts
	}
	return nil
}

func (x *Filter) GetActions() []Action {
	if x != nil {
		return x.Actions
	}
	return nil
}

func (x *Filter) GetPolicies() []*PolicyMatch {
	if x != nil {
		return x.Policies
	}
	return nil
}

type StringMatch struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Value         string                 `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	Type          MatchType              `protobuf:"varint,2,opt,name=type,proto3,enum=goldmane.MatchType" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StringMatch) Reset() {
	*x = StringMatch{}
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StringMatch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringMatch) ProtoMessage() {}

func (x *StringMatch) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringMatch.ProtoReflect.Descriptor instead.
func (*StringMatch) Descriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{9}
}

func (x *StringMatch) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *StringMatch) GetType() MatchType {
	if x != nil {
		return x.Type
	}
	return MatchType_Exact
}

type PortMatch struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Port          int64                  `protobuf:"varint,1,opt,name=port,proto3" json:"port,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PortMatch) Reset() {
	*x = PortMatch{}
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PortMatch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortMatch) ProtoMessage() {}

func (x *PortMatch) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortMatch.ProtoReflect.Descriptor instead.
func (*PortMatch) Descriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{10}
}

func (x *PortMatch) GetPort() int64 {
	if x != nil {
		return x.Port
	}
	return 0
}

type SortOption struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// SortBy declares the field by which to sort.
	SortBy        SortBy `protobuf:"varint,1,opt,name=sort_by,json=sortBy,proto3,enum=goldmane.SortBy" json:"sort_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SortOption) Reset() {
	*x = SortOption{}
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SortOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortOption) ProtoMessage() {}

func (x *SortOption) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortOption.ProtoReflect.Descriptor instead.
func (*SortOption) Descriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{11}
}

func (x *SortOption) GetSortBy() SortBy {
	if x != nil {
		return x.SortBy
	}
	return SortBy_Time
}

// PolicyMatch defines criteria for matching one or more policy rules within a Flow's
// policy trace.
type PolicyMatch struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Kind          PolicyKind             `protobuf:"varint,1,opt,name=kind,proto3,enum=goldmane.PolicyKind" json:"kind,omitempty"`
	Tier          string                 `protobuf:"bytes,2,opt,name=tier,proto3" json:"tier,omitempty"`
	Namespace     string                 `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Action        Action                 `protobuf:"varint,5,opt,name=action,proto3,enum=goldmane.Action" json:"action,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PolicyMatch) Reset() {
	*x = PolicyMatch{}
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PolicyMatch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PolicyMatch) ProtoMessage() {}

func (x *PolicyMatch) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PolicyMatch.ProtoReflect.Descriptor instead.
func (*PolicyMatch) Descriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{12}
}

func (x *PolicyMatch) GetKind() PolicyKind {
	if x != nil {
		return x.Kind
	}
	return PolicyKind_KindUnspecified
}

func (x *PolicyMatch) GetTier() string {
	if x != nil {
		return x.Tier
	}
	return ""
}

func (x *PolicyMatch) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *PolicyMatch) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PolicyMatch) GetAction() Action {
	if x != nil {
		return x.Action
	}
	return Action_ActionUnspecified
}

// FlowReceipt is a response from the server to a client after publishing a Flow.
type FlowReceipt struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FlowReceipt) Reset() {
	*x = FlowReceipt{}
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FlowReceipt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlowReceipt) ProtoMessage() {}

func (x *FlowReceipt) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlowReceipt.ProtoReflect.Descriptor instead.
func (*FlowReceipt) Descriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{13}
}

// FlowUpdate wraps a Flow with additional metadata.
type FlowUpdate struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Flow contains the actual flow being sent.
	Flow          *Flow `protobuf:"bytes,1,opt,name=flow,proto3" json:"flow,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FlowUpdate) Reset() {
	*x = FlowUpdate{}
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FlowUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlowUpdate) ProtoMessage() {}

func (x *FlowUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlowUpdate.ProtoReflect.Descriptor instead.
func (*FlowUpdate) Descriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{14}
}

func (x *FlowUpdate) GetFlow() *Flow {
	if x != nil {
		return x.Flow
	}
	return nil
}

// FlowKey includes the identifying fields for a Flow.
// - Source: Name, namespace, type, and labels.
// - Destination: Name, namespace, type, labels and port
// - Action taken on the connection.
// - Reporter (i.e., measured at source or destination).
// - Protocol of the connection (TCP, UDP, etc.).
type FlowKey struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// SourceName is the name of the source for this Flow.
	// The value is contextualized by the source_type field:
	// - For WorkloadEndpoint, this represents a set of pods that share a GenerateName.
	// - For HostEndpoint, this is the host endpoint name.
	// - For NetworkSet, it is the name of the network set.
	// - For Network, this is either "pub" for a public network, or "pvt" for a private network.
	SourceName string `protobuf:"bytes,1,opt,name=source_name,json=sourceName,proto3" json:"source_name,omitempty"`
	// SourceNamespace is the namespace of the source pods for this flow.
	SourceNamespace string `protobuf:"bytes,2,opt,name=source_namespace,json=sourceNamespace,proto3" json:"source_namespace,omitempty"`
	// SourceType is the type of the source, used to contextualize the source
	// name and namespace fields.
	SourceType EndpointType `protobuf:"varint,3,opt,name=source_type,json=sourceType,proto3,enum=goldmane.EndpointType" json:"source_type,omitempty"`
	// DestName is the name of the destination for this Flow.
	// The value is contextualized by the source_type field:
	// - For WorkloadEndpoint, this represents a set of pods that share a GenerateName.
	// - For HostEndpoint, this is the host endpoint name.
	// - For NetworkSet, it is the name of the network set.
	// - For Network, this is either "pub" for a public network, or "pvt" for a private network.
	DestName string `protobuf:"bytes,4,opt,name=dest_name,json=destName,proto3" json:"dest_name,omitempty"`
	// DestNamespace is the namespace of the destination pods for this flow.
	DestNamespace string `protobuf:"bytes,5,opt,name=dest_namespace,json=destNamespace,proto3" json:"dest_namespace,omitempty"`
	// DestType is the type of the destination, used to contextualize the dest
	// name and namespace fields.
	DestType EndpointType `protobuf:"varint,6,opt,name=dest_type,json=destType,proto3,enum=goldmane.EndpointType" json:"dest_type,omitempty"`
	// DestPort is the destination port on the specified protocol accessed by this flow.
	DestPort int64 `protobuf:"varint,7,opt,name=dest_port,json=destPort,proto3" json:"dest_port,omitempty"`
	// DestServiceName is the name of the destination service, if any.
	DestServiceName string `protobuf:"bytes,8,opt,name=dest_service_name,json=destServiceName,proto3" json:"dest_service_name,omitempty"`
	// DestServiceNamespace is the namespace of the destination service, if any.
	DestServiceNamespace string `protobuf:"bytes,9,opt,name=dest_service_namespace,json=destServiceNamespace,proto3" json:"dest_service_namespace,omitempty"`
	// DestServicePortName is the name of the port on the destination service, if any.
	DestServicePortName string `protobuf:"bytes,10,opt,name=dest_service_port_name,json=destServicePortName,proto3" json:"dest_service_port_name,omitempty"`
	// DestServicePort is the port number on the destination service.
	DestServicePort int64 `protobuf:"varint,11,opt,name=dest_service_port,json=destServicePort,proto3" json:"dest_service_port,omitempty"`
	// Proto is the L4 protocol for this flow. For example, TCP, UDP, SCTP, ICMP.
	Proto string `protobuf:"bytes,12,opt,name=proto,proto3" json:"proto,omitempty"`
	// Reporter is either "src" or "dst", depending on whether this flow was generated
	// at the initiating or terminating end of the connection attempt.
	Reporter Reporter `protobuf:"varint,13,opt,name=reporter,proto3,enum=goldmane.Reporter" json:"reporter,omitempty"`
	// Action is the ultimate action taken on the flow.
	Action Action `protobuf:"varint,14,opt,name=action,proto3,enum=goldmane.Action" json:"action,omitempty"`
	// Policies includes an entry for each policy rule that took an action on the connections
	// aggregated into this flow.
	Policies      *PolicyTrace `protobuf:"bytes,15,opt,name=policies,proto3" json:"policies,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FlowKey) Reset() {
	*x = FlowKey{}
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FlowKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlowKey) ProtoMessage() {}

func (x *FlowKey) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlowKey.ProtoReflect.Descriptor instead.
func (*FlowKey) Descriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{15}
}

func (x *FlowKey) GetSourceName() string {
	if x != nil {
		return x.SourceName
	}
	return ""
}

func (x *FlowKey) GetSourceNamespace() string {
	if x != nil {
		return x.SourceNamespace
	}
	return ""
}

func (x *FlowKey) GetSourceType() EndpointType {
	if x != nil {
		return x.SourceType
	}
	return EndpointType_EndpointTypeUnspecified
}

func (x *FlowKey) GetDestName() string {
	if x != nil {
		return x.DestName
	}
	return ""
}

func (x *FlowKey) GetDestNamespace() string {
	if x != nil {
		return x.DestNamespace
	}
	return ""
}

func (x *FlowKey) GetDestType() EndpointType {
	if x != nil {
		return x.DestType
	}
	return EndpointType_EndpointTypeUnspecified
}

func (x *FlowKey) GetDestPort() int64 {
	if x != nil {
		return x.DestPort
	}
	return 0
}

func (x *FlowKey) GetDestServiceName() string {
	if x != nil {
		return x.DestServiceName
	}
	return ""
}

func (x *FlowKey) GetDestServiceNamespace() string {
	if x != nil {
		return x.DestServiceNamespace
	}
	return ""
}

func (x *FlowKey) GetDestServicePortName() string {
	if x != nil {
		return x.DestServicePortName
	}
	return ""
}

func (x *FlowKey) GetDestServicePort() int64 {
	if x != nil {
		return x.DestServicePort
	}
	return 0
}

func (x *FlowKey) GetProto() string {
	if x != nil {
		return x.Proto
	}
	return ""
}

func (x *FlowKey) GetReporter() Reporter {
	if x != nil {
		return x.Reporter
	}
	return Reporter_ReporterUnspecified
}

func (x *FlowKey) GetAction() Action {
	if x != nil {
		return x.Action
	}
	return Action_ActionUnspecified
}

func (x *FlowKey) GetPolicies() *PolicyTrace {
	if x != nil {
		return x.Policies
	}
	return nil
}

// Flow is a message representing statistics gathered about connections that share common fields,
// aggregated across either time, nodes, or both.
type Flow struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Key includes the identifying fields for this flow.
	Key *FlowKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
	// StartTime is the start time for this flow. It is represented as the number of
	// seconds since the UNIX epoch.
	StartTime int64 `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// EndTime is the end time for this flow. It is always at least one aggregation
	// interval after the start time.
	EndTime int64 `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// SourceLabels contains the intersection of labels that appear on all source
	// pods that contributed to this flow.
	SourceLabels []string `protobuf:"bytes,4,rep,name=source_labels,json=sourceLabels,proto3" json:"source_labels,omitempty"`
	// SourceLabels contains the intersection of labels that appear on all destination
	// pods that contributed to this flow.
	DestLabels []string `protobuf:"bytes,5,rep,name=dest_labels,json=destLabels,proto3" json:"dest_labels,omitempty"`
	// Statistics.
	PacketsIn  int64 `protobuf:"varint,6,opt,name=packets_in,json=packetsIn,proto3" json:"packets_in,omitempty"`
	PacketsOut int64 `protobuf:"varint,7,opt,name=packets_out,json=packetsOut,proto3" json:"packets_out,omitempty"`
	BytesIn    int64 `protobuf:"varint,8,opt,name=bytes_in,json=bytesIn,proto3" json:"bytes_in,omitempty"`
	BytesOut   int64 `protobuf:"varint,9,opt,name=bytes_out,json=bytesOut,proto3" json:"bytes_out,omitempty"`
	// NumConnectionsStarted tracks the total number of new connections recorded for this Flow. It counts each
	// connection attempt that matches the FlowKey that was made between this Flow's StartTime and EndTime.
	NumConnectionsStarted int64 `protobuf:"varint,10,opt,name=num_connections_started,json=numConnectionsStarted,proto3" json:"num_connections_started,omitempty"`
	// NumConnectionsCompleted tracks the total number of completed TCP connections recorded for this Flow. It counts each
	// connection that matches the FlowKey that was completed between this Flow's StartTime and EndTime.
	NumConnectionsCompleted int64 `protobuf:"varint,11,opt,name=num_connections_completed,json=numConnectionsCompleted,proto3" json:"num_connections_completed,omitempty"`
	// NumConnectionsLive tracks the total number of still active connections recorded for this Flow. It counts each
	// connection that matches the FlowKey that was active at this Flow's EndTime.
	NumConnectionsLive int64 `protobuf:"varint,12,opt,name=num_connections_live,json=numConnectionsLive,proto3" json:"num_connections_live,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *Flow) Reset() {
	*x = Flow{}
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Flow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Flow) ProtoMessage() {}

func (x *Flow) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Flow.ProtoReflect.Descriptor instead.
func (*Flow) Descriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{16}
}

func (x *Flow) GetKey() *FlowKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *Flow) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *Flow) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *Flow) GetSourceLabels() []string {
	if x != nil {
		return x.SourceLabels
	}
	return nil
}

func (x *Flow) GetDestLabels() []string {
	if x != nil {
		return x.DestLabels
	}
	return nil
}

func (x *Flow) GetPacketsIn() int64 {
	if x != nil {
		return x.PacketsIn
	}
	return 0
}

func (x *Flow) GetPacketsOut() int64 {
	if x != nil {
		return x.PacketsOut
	}
	return 0
}

func (x *Flow) GetBytesIn() int64 {
	if x != nil {
		return x.BytesIn
	}
	return 0
}

func (x *Flow) GetBytesOut() int64 {
	if x != nil {
		return x.BytesOut
	}
	return 0
}

func (x *Flow) GetNumConnectionsStarted() int64 {
	if x != nil {
		return x.NumConnectionsStarted
	}
	return 0
}

func (x *Flow) GetNumConnectionsCompleted() int64 {
	if x != nil {
		return x.NumConnectionsCompleted
	}
	return 0
}

func (x *Flow) GetNumConnectionsLive() int64 {
	if x != nil {
		return x.NumConnectionsLive
	}
	return 0
}

type PolicyTrace struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// EnforcedPolicies shows the active dataplane policy rules traversed by this Flow.
	EnforcedPolicies []*PolicyHit `protobuf:"bytes,1,rep,name=enforced_policies,json=enforcedPolicies,proto3" json:"enforced_policies,omitempty"`
	// PendingPolicies shows the expected policy rules traversed by this Flow when including
	// staged policies.
	PendingPolicies []*PolicyHit `protobuf:"bytes,2,rep,name=pending_policies,json=pendingPolicies,proto3" json:"pending_policies,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *PolicyTrace) Reset() {
	*x = PolicyTrace{}
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PolicyTrace) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PolicyTrace) ProtoMessage() {}

func (x *PolicyTrace) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PolicyTrace.ProtoReflect.Descriptor instead.
func (*PolicyTrace) Descriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{17}
}

func (x *PolicyTrace) GetEnforcedPolicies() []*PolicyHit {
	if x != nil {
		return x.EnforcedPolicies
	}
	return nil
}

func (x *PolicyTrace) GetPendingPolicies() []*PolicyHit {
	if x != nil {
		return x.PendingPolicies
	}
	return nil
}

// PolicyHit represents a policy rule that was traversed by this flow. It can be either an enforced policy hit
// from the dataplane, or a staged policy hit that is not yet active.
type PolicyHit struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Kind corresponds to the resource Kind for the policy.
	Kind PolicyKind `protobuf:"varint,1,opt,name=kind,proto3,enum=goldmane.PolicyKind" json:"kind,omitempty"`
	// Namespace is the Kubernetes namespace of the Policy, if namespaced. It is empty for global /
	// cluster-scoped policy kinds.
	Namespace string `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// Name is the Name of the policy object.
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// Tier is the Tier of the policy object.
	Tier string `protobuf:"bytes,4,opt,name=tier,proto3" json:"tier,omitempty"`
	// Action is the action taken by this policy rule.
	Action Action `protobuf:"varint,5,opt,name=action,proto3,enum=goldmane.Action" json:"action,omitempty"`
	// PolicyIndex is the order of the Policy among all policies traversed.
	PolicyIndex int64 `protobuf:"varint,6,opt,name=policy_index,json=policyIndex,proto3" json:"policy_index,omitempty"`
	// RuleIndex is the order of the Rule within the Policy rules.
	RuleIndex int64 `protobuf:"varint,7,opt,name=rule_index,json=ruleIndex,proto3" json:"rule_index,omitempty"`
	// Trigger indicates the first policy that selected this Flow and thus triggered the tier's
	// end-of-tier action. This is only valid for kind=EndOfTier, and is nil otherwise.
	Trigger       *PolicyHit `protobuf:"bytes,8,opt,name=trigger,proto3" json:"trigger,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PolicyHit) Reset() {
	*x = PolicyHit{}
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PolicyHit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PolicyHit) ProtoMessage() {}

func (x *PolicyHit) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PolicyHit.ProtoReflect.Descriptor instead.
func (*PolicyHit) Descriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{18}
}

func (x *PolicyHit) GetKind() PolicyKind {
	if x != nil {
		return x.Kind
	}
	return PolicyKind_KindUnspecified
}

func (x *PolicyHit) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *PolicyHit) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PolicyHit) GetTier() string {
	if x != nil {
		return x.Tier
	}
	return ""
}

func (x *PolicyHit) GetAction() Action {
	if x != nil {
		return x.Action
	}
	return Action_ActionUnspecified
}

func (x *PolicyHit) GetPolicyIndex() int64 {
	if x != nil {
		return x.PolicyIndex
	}
	return 0
}

func (x *PolicyHit) GetRuleIndex() int64 {
	if x != nil {
		return x.RuleIndex
	}
	return 0
}

func (x *PolicyHit) GetTrigger() *PolicyHit {
	if x != nil {
		return x.Trigger
	}
	return nil
}

type StatisticsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The start time from which to collect statistics (inclusive).
	//
	// - A value of zero indicates the oldest start time available by the server.
	// - A value greater than zero indicates an absolute time in seconds since the Unix epoch.
	// - A value less than zero indicates a relative number of seconds from "now", as determined by the server.
	StartTimeGte int64 `protobuf:"varint,1,opt,name=start_time_gte,json=startTimeGte,proto3" json:"start_time_gte,omitempty"`
	// The end time indicates the end of the windows from which to collect statistics.
	//
	// - A value of zero means "now", as determined by the server at the time of request.
	// - A value greater than zero indicates an absolute time in seconds since the Unix epoch.
	// - A value less than zero indicates a relative number of seconds from "now", as determined by the server.
	StartTimeLt int64 `protobuf:"varint,2,opt,name=start_time_lt,json=startTimeLt,proto3" json:"start_time_lt,omitempty"`
	// Type is the type of statistic to return. e.g., packets, bytes, etc.
	Type StatisticType `protobuf:"varint,3,opt,name=type,proto3,enum=goldmane.StatisticType" json:"type,omitempty"`
	// Configure statistics aggregation.
	// - Policy: each StatisticsResult will contain statistics for a particular policy.
	// - PolicyRule: each StatisticsResult will contain statistics for a particular policy rule.
	// - Any: return both per-Policy and per-PolicyRule results.
	GroupBy StatisticsGroupBy `protobuf:"varint,4,opt,name=group_by,json=groupBy,proto3,enum=goldmane.StatisticsGroupBy" json:"group_by,omitempty"`
	// Optionally configure fields to filter results. If provided, any policies not matching the PolicyMatch
	// will be omitted from the results.
	PolicyMatch *PolicyMatch `protobuf:"bytes,5,opt,name=policy_match,json=policyMatch,proto3" json:"policy_match,omitempty"`
	// TimeSeries configures whether or not to return time-series data in the response. If true,
	// the response will include multiple datapoints over the given time window. If false, data
	// across the time window will be aggregated into a single data point.
	TimeSeries    bool `protobuf:"varint,6,opt,name=time_series,json=timeSeries,proto3" json:"time_series,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StatisticsRequest) Reset() {
	*x = StatisticsRequest{}
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StatisticsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatisticsRequest) ProtoMessage() {}

func (x *StatisticsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatisticsRequest.ProtoReflect.Descriptor instead.
func (*StatisticsRequest) Descriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{19}
}

func (x *StatisticsRequest) GetStartTimeGte() int64 {
	if x != nil {
		return x.StartTimeGte
	}
	return 0
}

func (x *StatisticsRequest) GetStartTimeLt() int64 {
	if x != nil {
		return x.StartTimeLt
	}
	return 0
}

func (x *StatisticsRequest) GetType() StatisticType {
	if x != nil {
		return x.Type
	}
	return StatisticType_PacketCount
}

func (x *StatisticsRequest) GetGroupBy() StatisticsGroupBy {
	if x != nil {
		return x.GroupBy
	}
	return StatisticsGroupBy_Policy
}

func (x *StatisticsRequest) GetPolicyMatch() *PolicyMatch {
	if x != nil {
		return x.PolicyMatch
	}
	return nil
}

func (x *StatisticsRequest) GetTimeSeries() bool {
	if x != nil {
		return x.TimeSeries
	}
	return false
}

type StatisticsResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Policy identifies the policy / rule for which this data applies. Its meaning is contextualized
	// by the GroupBy field.
	//
	//   - StatisticsGroupBy_Policy: this field represents the specific Policy, and statistics are aggregated across all
	//     rules within that policy. Rule identifiers (Action, RuleID) will be omitted.
	//
	//   - StatisticsGroupBy_PolicyRule: this field identifies a specific rule within a Policy, and statistics are scoped to
	//     that particular rule.
	Policy *PolicyHit `protobuf:"bytes,1,opt,name=policy,proto3" json:"policy,omitempty"`
	// For statistics results targeting a specific policy rule, the direction
	// contextualizes the rule ID as either an ingress or egress rule.
	//
	// For statistics results grouped by policy, both ingress and egress statistics will be included.
	Direction RuleDirection `protobuf:"varint,2,opt,name=direction,proto3,enum=goldmane.RuleDirection" json:"direction,omitempty"`
	// GroupBy indicates whether the statistics in this result are aggregated for a policy, or for
	// a specific rule within that policy.
	GroupBy StatisticsGroupBy `protobuf:"varint,3,opt,name=group_by,json=groupBy,proto3,enum=goldmane.StatisticsGroupBy" json:"group_by,omitempty"`
	// Type indicates the type of data carried in this result. e.g., PacketCount vs ByteCount.
	Type StatisticType `protobuf:"varint,4,opt,name=type,proto3,enum=goldmane.StatisticType" json:"type,omitempty"`
	// AllowedIn contains the count of the requested statistic that was allowed for ingress flows.
	// The semantic meaning (e.g., packets vs bytes) is indicated by the Type field.
	AllowedIn  []int64 `protobuf:"varint,5,rep,packed,name=allowed_in,json=allowedIn,proto3" json:"allowed_in,omitempty"`
	AllowedOut []int64 `protobuf:"varint,6,rep,packed,name=allowed_out,json=allowedOut,proto3" json:"allowed_out,omitempty"`
	DeniedIn   []int64 `protobuf:"varint,7,rep,packed,name=denied_in,json=deniedIn,proto3" json:"denied_in,omitempty"`
	DeniedOut  []int64 `protobuf:"varint,8,rep,packed,name=denied_out,json=deniedOut,proto3" json:"denied_out,omitempty"`
	PassedIn   []int64 `protobuf:"varint,9,rep,packed,name=passed_in,json=passedIn,proto3" json:"passed_in,omitempty"`
	PassedOut  []int64 `protobuf:"varint,10,rep,packed,name=passed_out,json=passedOut,proto3" json:"passed_out,omitempty"`
	// X is the x axis of the data for time-series data. i.e., the timestamp. For non-timeseries data,
	// this will be nil.
	X             []int64 `protobuf:"varint,11,rep,packed,name=x,proto3" json:"x,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StatisticsResult) Reset() {
	*x = StatisticsResult{}
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StatisticsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatisticsResult) ProtoMessage() {}

func (x *StatisticsResult) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatisticsResult.ProtoReflect.Descriptor instead.
func (*StatisticsResult) Descriptor() ([]byte, []int) {
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP(), []int{20}
}

func (x *StatisticsResult) GetPolicy() *PolicyHit {
	if x != nil {
		return x.Policy
	}
	return nil
}

func (x *StatisticsResult) GetDirection() RuleDirection {
	if x != nil {
		return x.Direction
	}
	return RuleDirection_Any
}

func (x *StatisticsResult) GetGroupBy() StatisticsGroupBy {
	if x != nil {
		return x.GroupBy
	}
	return StatisticsGroupBy_Policy
}

func (x *StatisticsResult) GetType() StatisticType {
	if x != nil {
		return x.Type
	}
	return StatisticType_PacketCount
}

func (x *StatisticsResult) GetAllowedIn() []int64 {
	if x != nil {
		return x.AllowedIn
	}
	return nil
}

func (x *StatisticsResult) GetAllowedOut() []int64 {
	if x != nil {
		return x.AllowedOut
	}
	return nil
}

func (x *StatisticsResult) GetDeniedIn() []int64 {
	if x != nil {
		return x.DeniedIn
	}
	return nil
}

func (x *StatisticsResult) GetDeniedOut() []int64 {
	if x != nil {
		return x.DeniedOut
	}
	return nil
}

func (x *StatisticsResult) GetPassedIn() []int64 {
	if x != nil {
		return x.PassedIn
	}
	return nil
}

func (x *StatisticsResult) GetPassedOut() []int64 {
	if x != nil {
		return x.PassedOut
	}
	return nil
}

func (x *StatisticsResult) GetX() []int64 {
	if x != nil {
		return x.X
	}
	return nil
}

var File_internal_cniwatcher_calico_goldmane_api_proto protoreflect.FileDescriptor

const file_internal_cniwatcher_calico_goldmane_api_proto_rawDesc = "" +
	"\n" +
	"-internal/cniwatcher/calico/goldmane/api.proto\x12\bgoldmane\"\x98\x02\n" +
	"\x0fFlowListRequest\x12$\n" +
	"\x0estart_time_gte\x18\x01 \x01(\x03R\fstartTimeGte\x12\"\n" +
	"\rstart_time_lt\x18\x02 \x01(\x03R\vstartTimeLt\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x03R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x03R\bpageSize\x12-\n" +
	"\asort_by\x18\x05 \x03(\v2\x14.goldmane.SortOptionR\x06sortBy\x12(\n" +
	"\x06filter\x18\x06 \x01(\v2\x10.goldmane.FilterR\x06filter\x121\n" +
	"\x14aggregation_interval\x18\a \x01(\x03R\x13aggregationInterval\"h\n" +
	"\x0eFlowListResult\x12*\n" +
	"\x04meta\x18\x01 \x01(\v2\x16.goldmane.ListMetadataR\x04meta\x12*\n" +
	"\x05flows\x18\x02 \x03(\v2\x14.goldmane.FlowResultR\x05flows\"\x96\x01\n" +
	"\x11FlowStreamRequest\x12$\n" +
	"\x0estart_time_gte\x18\x01 \x01(\x03R\fstartTimeGte\x12(\n" +
	"\x06filter\x18\x02 \x01(\v2\x10.goldmane.FilterR\x06filter\x121\n" +
	"\x14aggregation_interval\x18\x03 \x01(\x03R\x13aggregationInterval\"\xe3\x01\n" +
	"\x12FilterHintsRequest\x12(\n" +
	"\x04type\x18\x01 \x01(\x0e2\x14.goldmane.FilterTypeR\x04type\x12(\n" +
	"\x06filter\x18\x02 \x01(\v2\x10.goldmane.FilterR\x06filter\x12$\n" +
	"\x0estart_time_gte\x18\x03 \x01(\x03R\fstartTimeGte\x12\"\n" +
	"\rstart_time_lt\x18\x04 \x01(\x03R\vstartTimeLt\x12\x12\n" +
	"\x04page\x18\x05 \x01(\x03R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x06 \x01(\x03R\bpageSize\"k\n" +
	"\x11FilterHintsResult\x12*\n" +
	"\x04meta\x18\x01 \x01(\v2\x16.goldmane.ListMetadataR\x04meta\x12*\n" +
	"\x05hints\x18\x02 \x03(\v2\x14.goldmane.FilterHintR\x05hints\"R\n" +
	"\fListMetadata\x12\x1e\n" +
	"\n" +
	"totalPages\x18\x01 \x01(\x03R\n" +
	"totalPages\x12\"\n" +
	"\ftotalResults\x18\x02 \x01(\x03R\ftotalResults\"\"\n" +
	"\n" +
	"FilterHint\x12\x14\n" +
	"\x05value\x18\x01 \x01(\tR\x05value\"@\n" +
	"\n" +
	"FlowResult\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\"\n" +
	"\x04flow\x18\x02 \x01(\v2\x0e.goldmane.FlowR\x04flow\"\xc4\x03\n" +
	"\x06Filter\x128\n" +
	"\fsource_names\x18\x01 \x03(\v2\x15.goldmane.StringMatchR\vsourceNames\x12B\n" +
	"\x11source_namespaces\x18\x02 \x03(\v2\x15.goldmane.StringMatchR\x10sourceNamespaces\x124\n" +
	"\n" +
	"dest_names\x18\x03 \x03(\v2\x15.goldmane.StringMatchR\tdestNames\x12>\n" +
	"\x0fdest_namespaces\x18\x04 \x03(\v2\x15.goldmane.StringMatchR\x0edestNamespaces\x123\n" +
	"\tprotocols\x18\x05 \x03(\v2\x15.goldmane.StringMatchR\tprotocols\x122\n" +
	"\n" +
	"dest_ports\x18\x06 \x03(\v2\x13.goldmane.PortMatchR\tdestPorts\x12*\n" +
	"\aactions\x18\a \x03(\x0e2\x10.goldmane.ActionR\aactions\x121\n" +
	"\bpolicies\x18\b \x03(\v2\x15.goldmane.PolicyMatchR\bpolicies\"L\n" +
	"\vStringMatch\x12\x14\n" +
	"\x05value\x18\x01 \x01(\tR\x05value\x12'\n" +
	"\x04type\x18\x02 \x01(\x0e2\x13.goldmane.MatchTypeR\x04type\"\x1f\n" +
	"\tPortMatch\x12\x12\n" +
	"\x04port\x18\x01 \x01(\x03R\x04port\"7\n" +
	"\n" +
	"SortOption\x12)\n" +
	"\asort_by\x18\x01 \x01(\x0e2\x10.goldmane.SortByR\x06sortBy\"\xa7\x01\n" +
	"\vPolicyMatch\x12(\n" +
	"\x04kind\x18\x01 \x01(\x0e2\x14.goldmane.PolicyKindR\x04kind\x12\x12\n" +
	"\x04tier\x18\x02 \x01(\tR\x04tier\x12\x1c\n" +
	"\tnamespace\x18\x03 \x01(\tR\tnamespace\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12(\n" +
	"\x06action\x18\x05 \x01(\x0e2\x10.goldmane.ActionR\x06action\"\r\n" +
	"\vFlowReceipt\"0\n" +
	"\n" +
	"FlowUpdate\x12\"\n" +
	"\x04flow\x18\x01 \x01(\v2\x0e.goldmane.FlowR\x04flow\"\x8a\x05\n" +
	"\aFlowKey\x12\x1f\n" +
	"\vsource_name\x18\x01 \x01(\tR\n" +
	"sourceName\x12)\n" +
	"\x10source_namespace\x18\x02 \x01(\tR\x0fsourceNamespace\x127\n" +
	"\vsource_type\x18\x03 \x01(\x0e2\x16.goldmane.EndpointTypeR\n" +
	"sourceType\x12\x1b\n" +
	"\tdest_name\x18\x04 \x01(\tR\bdestName\x12%\n" +
	"\x0edest_namespace\x18\x05 \x01(\tR\rdestNamespace\x123\n" +
	"\tdest_type\x18\x06 \x01(\x0e2\x16.goldmane.EndpointTypeR\bdestType\x12\x1b\n" +
	"\tdest_port\x18\a \x01(\x03R\bdestPort\x12*\n" +
	"\x11dest_service_name\x18\b \x01(\tR\x0fdestServiceName\x124\n" +
	"\x16dest_service_namespace\x18\t \x01(\tR\x14destServiceNamespace\x123\n" +
	"\x16dest_service_port_name\x18\n" +
	" \x01(\tR\x13destServicePortName\x12*\n" +
	"\x11dest_service_port\x18\v \x01(\x03R\x0fdestServicePort\x12\x14\n" +
	"\x05proto\x18\f \x01(\tR\x05proto\x12.\n" +
	"\breporter\x18\r \x01(\x0e2\x12.goldmane.ReporterR\breporter\x12(\n" +
	"\x06action\x18\x0e \x01(\x0e2\x10.goldmane.ActionR\x06action\x121\n" +
	"\bpolicies\x18\x0f \x01(\v2\x15.goldmane.PolicyTraceR\bpolicies\"\xc9\x03\n" +
	"\x04Flow\x12#\n" +
	"\x03Key\x18\x01 \x01(\v2\x11.goldmane.FlowKeyR\x03Key\x12\x1d\n" +
	"\n" +
	"start_time\x18\x02 \x01(\x03R\tstartTime\x12\x19\n" +
	"\bend_time\x18\x03 \x01(\x03R\aendTime\x12#\n" +
	"\rsource_labels\x18\x04 \x03(\tR\fsourceLabels\x12\x1f\n" +
	"\vdest_labels\x18\x05 \x03(\tR\n" +
	"destLabels\x12\x1d\n" +
	"\n" +
	"packets_in\x18\x06 \x01(\x03R\tpacketsIn\x12\x1f\n" +
	"\vpackets_out\x18\a \x01(\x03R\n" +
	"packetsOut\x12\x19\n" +
	"\bbytes_in\x18\b \x01(\x03R\abytesIn\x12\x1b\n" +
	"\tbytes_out\x18\t \x01(\x03R\bbytesOut\x126\n" +
	"\x17num_connections_started\x18\n" +
	" \x01(\x03R\x15numConnectionsStarted\x12:\n" +
	"\x19num_connections_completed\x18\v \x01(\x03R\x17numConnectionsCompleted\x120\n" +
	"\x14num_connections_live\x18\f \x01(\x03R\x12numConnectionsLive\"\x8f\x01\n" +
	"\vPolicyTrace\x12@\n" +
	"\x11enforced_policies\x18\x01 \x03(\v2\x13.goldmane.PolicyHitR\x10enforcedPolicies\x12>\n" +
	"\x10pending_policies\x18\x02 \x03(\v2\x13.goldmane.PolicyHitR\x0fpendingPolicies\"\x96\x02\n" +
	"\tPolicyHit\x12(\n" +
	"\x04kind\x18\x01 \x01(\x0e2\x14.goldmane.PolicyKindR\x04kind\x12\x1c\n" +
	"\tnamespace\x18\x02 \x01(\tR\tnamespace\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x12\n" +
	"\x04tier\x18\x04 \x01(\tR\x04tier\x12(\n" +
	"\x06action\x18\x05 \x01(\x0e2\x10.goldmane.ActionR\x06action\x12!\n" +
	"\fpolicy_index\x18\x06 \x01(\x03R\vpolicyIndex\x12\x1d\n" +
	"\n" +
	"rule_index\x18\a \x01(\x03R\truleIndex\x12-\n" +
	"\atrigger\x18\b \x01(\v2\x13.goldmane.PolicyHitR\atrigger\"\x9d\x02\n" +
	"\x11StatisticsRequest\x12$\n" +
	"\x0estart_time_gte\x18\x01 \x01(\x03R\fstartTimeGte\x12\"\n" +
	"\rstart_time_lt\x18\x02 \x01(\x03R\vstartTimeLt\x12+\n" +
	"\x04type\x18\x03 \x01(\x0e2\x17.goldmane.StatisticTypeR\x04type\x126\n" +
	"\bgroup_by\x18\x04 \x01(\x0e2\x1b.goldmane.StatisticsGroupByR\agroupBy\x128\n" +
	"\fpolicy_match\x18\x05 \x01(\v2\x15.goldmane.PolicyMatchR\vpolicyMatch\x12\x1f\n" +
	"\vtime_series\x18\x06 \x01(\bR\n" +
	"timeSeries\"\xa1\x03\n" +
	"\x10StatisticsResult\x12+\n" +
	"\x06policy\x18\x01 \x01(\v2\x13.goldmane.PolicyHitR\x06policy\x125\n" +
	"\tdirection\x18\x02 \x01(\x0e2\x17.goldmane.RuleDirectionR\tdirection\x126\n" +
	"\bgroup_by\x18\x03 \x01(\x0e2\x1b.goldmane.StatisticsGroupByR\agroupBy\x12+\n" +
	"\x04type\x18\x04 \x01(\x0e2\x17.goldmane.StatisticTypeR\x04type\x12\x1d\n" +
	"\n" +
	"allowed_in\x18\x05 \x03(\x03R\tallowedIn\x12\x1f\n" +
	"\vallowed_out\x18\x06 \x03(\x03R\n" +
	"allowedOut\x12\x1b\n" +
	"\tdenied_in\x18\a \x03(\x03R\bdeniedIn\x12\x1d\n" +
	"\n" +
	"denied_out\x18\b \x03(\x03R\tdeniedOut\x12\x1b\n" +
	"\tpassed_in\x18\t \x03(\x03R\bpassedIn\x12\x1d\n" +
	"\n" +
	"passed_out\x18\n" +
	" \x03(\x03R\tpassedOut\x12\f\n" +
	"\x01x\x18\v \x03(\x03R\x01x*\xc9\x01\n" +
	"\n" +
	"FilterType\x12\x19\n" +
	"\x15FilterTypeUnspecified\x10\x00\x12\x16\n" +
	"\x12FilterTypeDestName\x10\x01\x12\x18\n" +
	"\x14FilterTypeSourceName\x10\x02\x12\x1b\n" +
	"\x17FilterTypeDestNamespace\x10\x03\x12\x1d\n" +
	"\x19FilterTypeSourceNamespace\x10\x04\x12\x18\n" +
	"\x14FilterTypePolicyTier\x10\x05\x12\x18\n" +
	"\x14FilterTypePolicyName\x10\x06*>\n" +
	"\x06Action\x12\x15\n" +
	"\x11ActionUnspecified\x10\x00\x12\t\n" +
	"\x05Allow\x10\x01\x12\b\n" +
	"\x04Deny\x10\x02\x12\b\n" +
	"\x04Pass\x10\x03*!\n" +
	"\tMatchType\x12\t\n" +
	"\x05Exact\x10\x00\x12\t\n" +
	"\x05Fuzzy\x10\x01*\x95\x02\n" +
	"\n" +
	"PolicyKind\x12\x13\n" +
	"\x0fKindUnspecified\x10\x00\x12\x17\n" +
	"\x13CalicoNetworkPolicy\x10\x01\x12\x17\n" +
	"\x13GlobalNetworkPolicy\x10\x02\x12\x17\n" +
	"\x13StagedNetworkPolicy\x10\x03\x12\x1d\n" +
	"\x19StagedGlobalNetworkPolicy\x10\x04\x12!\n" +
	"\x1dStagedKubernetesNetworkPolicy\x10\x05\x12\x11\n" +
	"\rNetworkPolicy\x10\x06\x12\x16\n" +
	"\x12AdminNetworkPolicy\x10\a\x12\x1e\n" +
	"\x1aBaselineAdminNetworkPolicy\x10\b\x12\v\n" +
	"\aProfile\x10\t\x12\r\n" +
	"\tEndOfTier\x10\n" +
	"*v\n" +
	"\x06SortBy\x12\b\n" +
	"\x04Time\x10\x00\x12\f\n" +
	"\bDestName\x10\x01\x12\x11\n" +
	"\rDestNamespace\x10\x02\x12\f\n" +
	"\bDestType\x10\x03\x12\x0e\n" +
	"\n" +
	"SourceName\x10\x04\x12\x13\n" +
	"\x0fSourceNamespace\x10\x05\x12\x0e\n" +
	"\n" +
	"SourceType\x10\x06*p\n" +
	"\fEndpointType\x12\x1b\n" +
	"\x17EndpointTypeUnspecified\x10\x00\x12\x14\n" +
	"\x10WorkloadEndpoint\x10\x01\x12\x10\n" +
	"\fHostEndpoint\x10\x02\x12\x0e\n" +
	"\n" +
	"NetworkSet\x10\x03\x12\v\n" +
	"\aNetwork\x10\x04*5\n" +
	"\bReporter\x12\x17\n" +
	"\x13ReporterUnspecified\x10\x00\x12\a\n" +
	"\x03Src\x10\x01\x12\a\n" +
	"\x03Dst\x10\x02*H\n" +
	"\rStatisticType\x12\x0f\n" +
	"\vPacketCount\x10\x00\x12\r\n" +
	"\tByteCount\x10\x01\x12\x17\n" +
	"\x13LiveConnectionCount\x10\x02*/\n" +
	"\x11StatisticsGroupBy\x12\n" +
	"\n" +
	"\x06Policy\x10\x00\x12\x0e\n" +
	"\n" +
	"PolicyRule\x10\x01*1\n" +
	"\rRuleDirection\x12\a\n" +
	"\x03Any\x10\x00\x12\v\n" +
	"\aIngress\x10\x01\x12\n" +
	"\n" +
	"\x06Egress\x10\x022\xcd\x01\n" +
	"\x05Flows\x12;\n" +
	"\x04List\x12\x19.goldmane.FlowListRequest\x1a\x18.goldmane.FlowListResult\x12=\n" +
	"\x06Stream\x12\x1b.goldmane.FlowStreamRequest\x1a\x14.goldmane.FlowResult0\x01\x12H\n" +
	"\vFilterHints\x12\x1c.goldmane.FilterHintsRequest\x1a\x1b.goldmane.FilterHintsResult2K\n" +
	"\rFlowCollector\x12:\n" +
	"\aConnect\x12\x14.goldmane.FlowUpdate\x1a\x15.goldmane.FlowReceipt(\x010\x012O\n" +
	"\n" +
	"Statistics\x12A\n" +
	"\x04List\x12\x1b.goldmane.StatisticsRequest\x1a\x1a.goldmane.StatisticsResult0\x01B\tZ\a./protob\x06proto3"

var (
	file_internal_cniwatcher_calico_goldmane_api_proto_rawDescOnce sync.Once
	file_internal_cniwatcher_calico_goldmane_api_proto_rawDescData []byte
)

func file_internal_cniwatcher_calico_goldmane_api_proto_rawDescGZIP() []byte {
	file_internal_cniwatcher_calico_goldmane_api_proto_rawDescOnce.Do(func() {
		file_internal_cniwatcher_calico_goldmane_api_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_internal_cniwatcher_calico_goldmane_api_proto_rawDesc), len(file_internal_cniwatcher_calico_goldmane_api_proto_rawDesc)))
	})
	return file_internal_cniwatcher_calico_goldmane_api_proto_rawDescData
}

var file_internal_cniwatcher_calico_goldmane_api_proto_enumTypes = make([]protoimpl.EnumInfo, 10)
var file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_internal_cniwatcher_calico_goldmane_api_proto_goTypes = []any{
	(FilterType)(0),            // 0: goldmane.FilterType
	(Action)(0),                // 1: goldmane.Action
	(MatchType)(0),             // 2: goldmane.MatchType
	(PolicyKind)(0),            // 3: goldmane.PolicyKind
	(SortBy)(0),                // 4: goldmane.SortBy
	(EndpointType)(0),          // 5: goldmane.EndpointType
	(Reporter)(0),              // 6: goldmane.Reporter
	(StatisticType)(0),         // 7: goldmane.StatisticType
	(StatisticsGroupBy)(0),     // 8: goldmane.StatisticsGroupBy
	(RuleDirection)(0),         // 9: goldmane.RuleDirection
	(*FlowListRequest)(nil),    // 10: goldmane.FlowListRequest
	(*FlowListResult)(nil),     // 11: goldmane.FlowListResult
	(*FlowStreamRequest)(nil),  // 12: goldmane.FlowStreamRequest
	(*FilterHintsRequest)(nil), // 13: goldmane.FilterHintsRequest
	(*FilterHintsResult)(nil),  // 14: goldmane.FilterHintsResult
	(*ListMetadata)(nil),       // 15: goldmane.ListMetadata
	(*FilterHint)(nil),         // 16: goldmane.FilterHint
	(*FlowResult)(nil),         // 17: goldmane.FlowResult
	(*Filter)(nil),             // 18: goldmane.Filter
	(*StringMatch)(nil),        // 19: goldmane.StringMatch
	(*PortMatch)(nil),          // 20: goldmane.PortMatch
	(*SortOption)(nil),         // 21: goldmane.SortOption
	(*PolicyMatch)(nil),        // 22: goldmane.PolicyMatch
	(*FlowReceipt)(nil),        // 23: goldmane.FlowReceipt
	(*FlowUpdate)(nil),         // 24: goldmane.FlowUpdate
	(*FlowKey)(nil),            // 25: goldmane.FlowKey
	(*Flow)(nil),               // 26: goldmane.Flow
	(*PolicyTrace)(nil),        // 27: goldmane.PolicyTrace
	(*PolicyHit)(nil),          // 28: goldmane.PolicyHit
	(*StatisticsRequest)(nil),  // 29: goldmane.StatisticsRequest
	(*StatisticsResult)(nil),   // 30: goldmane.StatisticsResult
}
var file_internal_cniwatcher_calico_goldmane_api_proto_depIdxs = []int32{
	21, // 0: goldmane.FlowListRequest.sort_by:type_name -> goldmane.SortOption
	18, // 1: goldmane.FlowListRequest.filter:type_name -> goldmane.Filter
	15, // 2: goldmane.FlowListResult.meta:type_name -> goldmane.ListMetadata
	17, // 3: goldmane.FlowListResult.flows:type_name -> goldmane.FlowResult
	18, // 4: goldmane.FlowStreamRequest.filter:type_name -> goldmane.Filter
	0,  // 5: goldmane.FilterHintsRequest.type:type_name -> goldmane.FilterType
	18, // 6: goldmane.FilterHintsRequest.filter:type_name -> goldmane.Filter
	15, // 7: goldmane.FilterHintsResult.meta:type_name -> goldmane.ListMetadata
	16, // 8: goldmane.FilterHintsResult.hints:type_name -> goldmane.FilterHint
	26, // 9: goldmane.FlowResult.flow:type_name -> goldmane.Flow
	19, // 10: goldmane.Filter.source_names:type_name -> goldmane.StringMatch
	19, // 11: goldmane.Filter.source_namespaces:type_name -> goldmane.StringMatch
	19, // 12: goldmane.Filter.dest_names:type_name -> goldmane.StringMatch
	19, // 13: goldmane.Filter.dest_namespaces:type_name -> goldmane.StringMatch
	19, // 14: goldmane.Filter.protocols:type_name -> goldmane.StringMatch
	20, // 15: goldmane.Filter.dest_ports:type_name -> goldmane.PortMatch
	1,  // 16: goldmane.Filter.actions:type_name -> goldmane.Action
	22, // 17: goldmane.Filter.policies:type_name -> goldmane.PolicyMatch
	2,  // 18: goldmane.StringMatch.type:type_name -> goldmane.MatchType
	4,  // 19: goldmane.SortOption.sort_by:type_name -> goldmane.SortBy
	3,  // 20: goldmane.PolicyMatch.kind:type_name -> goldmane.PolicyKind
	1,  // 21: goldmane.PolicyMatch.action:type_name -> goldmane.Action
	26, // 22: goldmane.FlowUpdate.flow:type_name -> goldmane.Flow
	5,  // 23: goldmane.FlowKey.source_type:type_name -> goldmane.EndpointType
	5,  // 24: goldmane.FlowKey.dest_type:type_name -> goldmane.EndpointType
	6,  // 25: goldmane.FlowKey.reporter:type_name -> goldmane.Reporter
	1,  // 26: goldmane.FlowKey.action:type_name -> goldmane.Action
	27, // 27: goldmane.FlowKey.policies:type_name -> goldmane.PolicyTrace
	25, // 28: goldmane.Flow.Key:type_name -> goldmane.FlowKey
	28, // 29: goldmane.PolicyTrace.enforced_policies:type_name -> goldmane.PolicyHit
	28, // 30: goldmane.PolicyTrace.pending_policies:type_name -> goldmane.PolicyHit
	3,  // 31: goldmane.PolicyHit.kind:type_name -> goldmane.PolicyKind
	1,  // 32: goldmane.PolicyHit.action:type_name -> goldmane.Action
	28, // 33: goldmane.PolicyHit.trigger:type_name -> goldmane.PolicyHit
	7,  // 34: goldmane.StatisticsRequest.type:type_name -> goldmane.StatisticType
	8,  // 35: goldmane.StatisticsRequest.group_by:type_name -> goldmane.StatisticsGroupBy
	22, // 36: goldmane.StatisticsRequest.policy_match:type_name -> goldmane.PolicyMatch
	28, // 37: goldmane.StatisticsResult.policy:type_name -> goldmane.PolicyHit
	9,  // 38: goldmane.StatisticsResult.direction:type_name -> goldmane.RuleDirection
	8,  // 39: goldmane.StatisticsResult.group_by:type_name -> goldmane.StatisticsGroupBy
	7,  // 40: goldmane.StatisticsResult.type:type_name -> goldmane.StatisticType
	10, // 41: goldmane.Flows.List:input_type -> goldmane.FlowListRequest
	12, // 42: goldmane.Flows.Stream:input_type -> goldmane.FlowStreamRequest
	13, // 43: goldmane.Flows.FilterHints:input_type -> goldmane.FilterHintsRequest
	24, // 44: goldmane.FlowCollector.Connect:input_type -> goldmane.FlowUpdate
	29, // 45: goldmane.Statistics.List:input_type -> goldmane.StatisticsRequest
	11, // 46: goldmane.Flows.List:output_type -> goldmane.FlowListResult
	17, // 47: goldmane.Flows.Stream:output_type -> goldmane.FlowResult
	14, // 48: goldmane.Flows.FilterHints:output_type -> goldmane.FilterHintsResult
	23, // 49: goldmane.FlowCollector.Connect:output_type -> goldmane.FlowReceipt
	30, // 50: goldmane.Statistics.List:output_type -> goldmane.StatisticsResult
	46, // [46:51] is the sub-list for method output_type
	41, // [41:46] is the sub-list for method input_type
	41, // [41:41] is the sub-list for extension type_name
	41, // [41:41] is the sub-list for extension extendee
	0,  // [0:41] is the sub-list for field type_name
}

func init() { file_internal_cniwatcher_calico_goldmane_api_proto_init() }
func file_internal_cniwatcher_calico_goldmane_api_proto_init() {
	if File_internal_cniwatcher_calico_goldmane_api_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_internal_cniwatcher_calico_goldmane_api_proto_rawDesc), len(file_internal_cniwatcher_calico_goldmane_api_proto_rawDesc)),
			NumEnums:      10,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   3,
		},
		GoTypes:           file_internal_cniwatcher_calico_goldmane_api_proto_goTypes,
		DependencyIndexes: file_internal_cniwatcher_calico_goldmane_api_proto_depIdxs,
		EnumInfos:         file_internal_cniwatcher_calico_goldmane_api_proto_enumTypes,
		MessageInfos:      file_internal_cniwatcher_calico_goldmane_api_proto_msgTypes,
	}.Build()
	File_internal_cniwatcher_calico_goldmane_api_proto = out.File
	file_internal_cniwatcher_calico_goldmane_api_proto_goTypes = nil
	file_internal_cniwatcher_calico_goldmane_api_proto_depIdxs = nil
}
