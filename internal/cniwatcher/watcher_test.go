package cniwatcher

import (
	"context"
	"os"
	"testing"

	"github.com/go-logr/logr"
	"github.com/stretchr/testify/assert"
	"k8s.io/client-go/kubernetes/fake"
	"k8s.io/client-go/rest"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func TestNewCNIWatcher(t *testing.T) {
	tests := []struct {
		name        string
		nodeName    string
		cniType     string
		wantErr     bool
		expectedCNI CNIType
	}{
		{
			name:        "Valid Calico CNI",
			nodeName:    "test-node",
			cniType:     "calico",
			wantErr:     false,
			expectedCNI: CNITypeCalico,
		},
		{
			name:        "Valid Cilium CNI",
			nodeName:    "test-node",
			cniType:     "cilium",
			wantErr:     false,
			expectedCNI: CNITypeCilium,
		},
		{
			name:     "Missing Node Name",
			nodeName: "",
			cniType:  "calico",
			wantErr:  true,
		},
		{
			name:        "Unknown CNI Type",
			nodeName:    "test-node",
			cniType:     "unknown",
			wantErr:     false,
			expectedCNI: CNITypeUnknown,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.nodeName != "" {
				os.Setenv("NODE_NAME", tt.nodeName)
				defer os.Unsetenv("NODE_NAME")
			}
			if tt.cniType != "" {
				os.Setenv("CNI_TYPE", tt.cniType)
				defer os.Unsetenv("CNI_TYPE")
			}

			client := fake.NewSimpleClientset()
			config := &rest.Config{}
			log := logr.Discard()

			watcher, err := NewCNIWatcher(client, config, log)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, watcher)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, watcher)
				if baseWatcher, ok := watcher.(*BaseWatcher); ok {
					assert.Equal(t, tt.expectedCNI, baseWatcher.cniType)
				}
			}
		})
	}
}

func TestPoliciesToStrings(t *testing.T) {
	tests := []struct {
		name     string
		policies []Policy
		want     []string
	}{
		{
			name:     "Empty policies",
			policies: []Policy{},
			want:     []string{},
		},
		{
			name: "Single policy",
			policies: []Policy{
				{
					TypeMeta: metav1.TypeMeta{
						APIVersion: "v1",
						Kind:       "NetworkPolicy",
					},
					Name:      "test-policy",
					Namespace: "default",
				},
			},
			want: []string{"v1/NetworkPolicy/default/test-policy"},
		},
		{
			name: "Multiple policies",
			policies: []Policy{
				{
					TypeMeta: metav1.TypeMeta{
						APIVersion: "v1",
						Kind:       "NetworkPolicy",
					},
					Name:       "policy1",
					Namespace:  "default",
				},
				{
					TypeMeta: metav1.TypeMeta{
						APIVersion: "v1",
						Kind:       "NetworkPolicy",
					},
					Name:       "policy2",
					Namespace:  "kube-system",
				},
			},
			want: []string{
				"v1/NetworkPolicy/default/policy1",
				"v1/NetworkPolicy/kube-system/policy2",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := policiesToStrings(tt.policies)
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestBaseWatcher_InitOtel(t *testing.T) {
	tests := []struct {
		name             string
		collectorEnv     string
		wantErr          bool
		expectedEndpoint string
	}{
		{
			name:             "Default collector endpoint",
			collectorEnv:     "",
			wantErr:          false,
			expectedEndpoint: "otel-collector:4317",
		},
		{
			name:             "Custom collector endpoint",
			collectorEnv:     "custom-collector:4317",
			wantErr:          false,
			expectedEndpoint: "custom-collector:4317",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.collectorEnv != "" {
				os.Setenv("OTEL_EXPORTER_OTLP_ENDPOINT", tt.collectorEnv)
				defer os.Unsetenv("OTEL_EXPORTER_OTLP_ENDPOINT")
			}

			watcher := &BaseWatcher{
				nodeName: "test-node",
				cniType:  CNITypeCalico,
				log:      logr.Discard(),
			}

			err := watcher.InitOtel(context.Background())
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, watcher.tracerProvider)
				assert.NotNil(t, watcher.tracer)
			}
		})
	}
}
