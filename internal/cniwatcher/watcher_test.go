package cniwatcher

import (
	"context"
	"testing"

	"github.com/neuvector/network-enforcement/internal/otel"
	"github.com/neuvector/network-enforcement/internal/types"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes/fake"
	"k8s.io/client-go/rest"
)

func TestNewCNIWatcher(t *testing.T) {
	tests := []struct {
		name        string
		nodeName    string
		cniType     CNIType
		wantErr     bool
		expectedCNI CNIType
	}{
		{
			name:        "Valid Calico CNI",
			nodeName:    "test-node",
			cniType:     CNITypeCalico,
			wantErr:     false,
			expectedCNI: CNITypeCalico,
		},
		{
			name:        "Valid Cilium CNI",
			nodeName:    "test-node",
			cniType:     CNITypeCilium,
			wantErr:     false,
			expectedCNI: CNITypeCilium,
		},
		{
			name:     "Missing Node Name",
			nodeName: "",
			cniType:  CNITypeCalico,
			wantErr:  true,
		},
		{
			name:     "Unknown CNI Type",
			nodeName: "test-node",
			cniType:  CNITypeUnknown,
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client := fake.NewSimpleClientset()
			config := &rest.Config{}
			log := logrus.New()
			otelService := otel.NewOpenTelemetryService(context.Background(), log)

			cfg := CNIWatcherConfig{
				Ctx:         context.Background(),
				Clientset:   client,
				Config:      config,
				Log:         log,
				NodeName:    tt.nodeName,
				CNIType:     tt.cniType,
				OtelService: otelService,
			}
			watcher, err := NewCNIWatcher(cfg)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, watcher)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, watcher)
				// We can't easily test the internal cniType anymore since it's encapsulated
				// The fact that NewCNIWatcher succeeded means the correct type was created
			}
		})
	}
}

func TestPoliciesToStrings(t *testing.T) {
	tests := []struct {
		name     string
		policies []types.Policy
		want     []string
	}{
		{
			name:     "Empty policies",
			policies: []types.Policy{},
			want:     []string{},
		},
		{
			name: "Single policy",
			policies: []types.Policy{
				{
					TypeMeta: metav1.TypeMeta{
						APIVersion: "v1",
						Kind:       "NetworkPolicy",
					},
					Name:      "test-policy",
					Namespace: "default",
				},
			},
			want: []string{"v1/NetworkPolicy/default/test-policy"},
		},
		{
			name: "Multiple policies",
			policies: []types.Policy{
				{
					TypeMeta: metav1.TypeMeta{
						APIVersion: "v1",
						Kind:       "NetworkPolicy",
					},
					Name:      "policy1",
					Namespace: "default",
				},
				{
					TypeMeta: metav1.TypeMeta{
						APIVersion: "v1",
						Kind:       "NetworkPolicy",
					},
					Name:      "policy2",
					Namespace: "kube-system",
				},
			},
			want: []string{
				"v1/NetworkPolicy/default/policy1",
				"v1/NetworkPolicy/kube-system/policy2",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Note: policiesToStrings is now in the otel package
			// This test is testing the Policy.String() method indirectly
			got := make([]string, len(tt.policies))
			for i, p := range tt.policies {
				got[i] = p.String()
			}
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestGetNetworkPolicyAPIVersion(t *testing.T) {
	tests := []struct {
		name    string
		kind    string
		want    string
		wantErr bool
	}{
		{
			name:    "NetworkPolicy",
			kind:    "NetworkPolicy",
			want:    "networking.k8s.io/v1",
			wantErr: false,
		},
		{
			name:    "CalicoNetworkPolicy",
			kind:    "CalicoNetworkPolicy",
			want:    "projectcalico.org/v3",
			wantErr: false,
		},
		{
			name:    "GlobalNetworkPolicy",
			kind:    "GlobalNetworkPolicy",
			want:    "projectcalico.org/v3",
			wantErr: false,
		},
		{
			name:    "CiliumNetworkPolicy",
			kind:    "CiliumNetworkPolicy",
			want:    "cilium.io/v2",
			wantErr: false,
		},
		{
			name:    "CiliumClusterwideNetworkPolicy",
			kind:    "CiliumClusterwideNetworkPolicy",
			want:    "cilium.io/v2",
			wantErr: false,
		},
		{
			name:    "Unknown policy kind",
			kind:    "UnknownPolicy",
			want:    "",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cfg := &CNIWatcherConfig{}
			got, err := cfg.GetNetworkPolicyAPIVersion(tt.kind)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}
		})
	}
}

func TestResolvePodOrServiceByIP(t *testing.T) {
	tests := []struct {
		name    string
		ip      string
		want    PodOrServiceInfo
		wantErr bool
	}{
		{
			name:    "Empty IP",
			ip:      "",
			want:    PodOrServiceInfo{},
			wantErr: true,
		},
		{
			name:    "Invalid IP format",
			ip:      "invalid-ip",
			want:    PodOrServiceInfo{},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cfg := &CNIWatcherConfig{
				Ctx: context.Background(),
			}
			got, err := cfg.ResolvePodOrServiceByIP(tt.ip)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}
		})
	}
}
