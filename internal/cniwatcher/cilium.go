package cniwatcher

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	flowpb "github.com/cilium/cilium/api/v1/flow"
	hubbleObserver "github.com/cilium/cilium/api/v1/observer"
	monitorApi "github.com/cilium/cilium/pkg/monitor/api"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

const (
	DefaultHubbleEndpoint = "unix:///var/run/cilium/hubble.sock"
)

type CiliumWatcher struct {
	log            *logrus.Logger
	telemetry      *OpenTelemetryService
	policyService  *PolicyEventService
	hubbleEndpoint string
	client         hubbleObserver.ObserverClient
	conn           *grpc.ClientConn
}

func NewCiliumWatcher(cfg CNIWatcherConfig) (*CiliumWatcher, error) {
	if cfg.NodeName == "" {
		return nil, fmt.Errorf("node name cannot be empty")
	}

	telemetry := NewOpenTelemetryService(cfg.Log, cfg.NodeName, CNITypeCilium)
	policyService := NewPolicyEventService(cfg.Clientset, cfg.Config, cfg.Log, telemetry)

	var hubbleEndpoint string
	if endpoint := os.Getenv("HUBBLE_ENDPOINT"); endpoint != "" {
		hubbleEndpoint = endpoint
	} else {
		hubbleEndpoint = DefaultHubbleEndpoint
	}

	watcher := &CiliumWatcher{
		log:            cfg.Log,
		telemetry:      telemetry,
		policyService:  policyService,
		hubbleEndpoint: hubbleEndpoint,
	}

	if err := telemetry.Init(context.Background()); err != nil {
		cfg.Log.Error(err, "Failed to initialize OpenTelemetry, continuing without tracing")
	}

	return watcher, nil
}

func (w *CiliumWatcher) Start(ctx context.Context) error {
	w.log.Info("Starting cniWatcher", "node", w.telemetry.nodeName, "cniType", w.telemetry.cniType)

	doneCh := make(chan struct{})

	go func() {
		defer close(doneCh)
		retryDelay := 5 * time.Second
		maxRetryDelay := 30 * time.Second

		for {
			select {
			case <-ctx.Done():
				return
			default:
				if w.client == nil {
					if err := w.connectToHubble(); err != nil {
						w.log.Error(err, "Failed to connect to Hubble, will retry")
						time.Sleep(retryDelay)
						retryDelay = time.Duration(float64(retryDelay) * 1.5)
						if retryDelay > maxRetryDelay {
							retryDelay = maxRetryDelay
						}
						continue
					}
					retryDelay = 5 * time.Second
				}

				if err := w.watchFlows(ctx); err != nil {
					if ctx.Err() == context.Canceled {
						return
					}
					w.log.Error(err, "Error watching flows, will retry")
					if w.conn != nil {
						w.conn.Close()
						w.conn = nil
						w.client = nil
					}
					time.Sleep(retryDelay)
					continue
				}
			}
		}
	}()

	<-ctx.Done()

	select {
	case <-doneCh:
		w.log.Info("Cilium cniWatcher shutdown complete")
	case <-time.After(5 * time.Second):
		w.log.Info("Cilium cniWatcher shutdown timed out")
	}

	return nil
}

func (w *CiliumWatcher) connectToHubble() error {
	maxRetries := 3
	retryDelay := 5 * time.Second

	var conn *grpc.ClientConn
	var err error

	for i := range maxRetries {
		conn, err = grpc.NewClient(
			w.hubbleEndpoint,
			grpc.WithTransportCredentials(insecure.NewCredentials()), // TODO: Replace with secure credentials (TLS/mTLS) for production use
		)

		if err == nil {
			break
		}

		w.log.Error(err, "Failed to connect to Hubble, retrying",
			"attempt", i+1,
			"maxRetries", maxRetries,
			"endpoint", w.hubbleEndpoint)

		time.Sleep(retryDelay)
	}

	if err != nil {
		return fmt.Errorf("failed to connect to Hubble after %d attempts: %w", maxRetries, err)
	}

	w.conn = conn
	w.client = hubbleObserver.NewObserverClient(conn)
	w.log.Info("Successfully connected to Hubble", "endpoint", w.hubbleEndpoint)

	return nil
}

func (w *CiliumWatcher) watchFlows(ctx context.Context) error {
	req := &hubbleObserver.GetFlowsRequest{
		Number: 0,
		Follow: true,
		Whitelist: []*flowpb.FlowFilter{
			{
				EventType: []*flowpb.EventTypeFilter{
					{Type: monitorApi.MessageTypePolicyVerdict},
				},
				DropReasonDesc: []flowpb.DropReason{
					flowpb.DropReason_POLICY_DENIED,
					flowpb.DropReason_POLICY_DENY,
				},
			},
		},
	}

	w.log.Info("Starting to watch Cilium flows from Hubble", "filter", "DROPPED events with policy deny reasons")
	client, err := w.client.GetFlows(ctx, req)
	if err != nil {
		return fmt.Errorf("failed to get flows from Hubble: %w", err)
	}

	for {
		select {
		case <-ctx.Done():
			return nil
		default:
			flow, err := client.Recv()
			if err != nil {
				if err.Error() == "EOF" {
					continue
				}
				if ctx.Err() == context.Canceled {
					return nil
				}
				return fmt.Errorf("error receiving flow from Hubble: %w", err)
			}

			err = w.parsePolicyDenyEvent(flow)
			if err != nil {
				w.log.Error(err, "failed to parse policy deny event", "flow", flow)
			}
		}
	}
}

func (w *CiliumWatcher) parsePolicyDenyEvent(flow *hubbleObserver.GetFlowsResponse) error {
	if flow == nil {
		return fmt.Errorf("flow is empty")
	}

	flowResponse, ok := flow.ResponseTypes.(*hubbleObserver.GetFlowsResponse_Flow)
	if !ok || flowResponse == nil || flowResponse.Flow == nil {
		return fmt.Errorf("flowResponse is empty")
	}

	f := flowResponse.Flow

	if f.EventType == nil ||
		(f.DropReasonDesc != flowpb.DropReason_POLICY_DENIED &&
			f.DropReasonDesc != flowpb.DropReason_POLICY_DENY) {
		return nil
	}

	var proto string
	var srcNamespace, srcName, dstNamespace, dstName string
	var srcLabels, dstLabels []string
	var srcWorkloads, dstWorkloads []string

	if f.Source != nil {
		srcNamespace = f.Source.Namespace
		srcName = f.Source.PodName
		srcLabels = f.Source.Labels
		if len(f.Source.Workloads) > 0 {
			for _, wl := range f.Source.Workloads {
				if wl.Kind != "" && wl.Name != "" {
					srcWorkloads = append(srcWorkloads, fmt.Sprintf("%s/%s", wl.Kind, wl.Name))
				}
			}
		}
	}

	if f.Destination != nil {
		dstNamespace = f.Destination.Namespace
		dstName = f.Destination.PodName
		dstLabels = f.Destination.Labels
		if len(f.Destination.Workloads) > 0 {
			for _, wl := range f.Destination.Workloads {
				if wl.Kind != "" && wl.Name != "" {
					dstWorkloads = append(dstWorkloads, fmt.Sprintf("%s/%s", wl.Kind, wl.Name))
				}
			}
		}
	}

	if f.L4 != nil {
		switch f.L4.Protocol.(type) {
		case *flowpb.Layer4_TCP:
			proto = "TCP"
		case *flowpb.Layer4_UDP:
			proto = "UDP"
		case *flowpb.Layer4_ICMPv4, *flowpb.Layer4_ICMPv6:
			proto = "ICMP"
		case *flowpb.Layer4_SCTP:
			proto = "SCTP"
		default:
			proto = "Unknown"
		}
	}

	var egressPolicies, ingressPolicies []Policy
	if len(f.EgressDeniedBy) > 0 {
		for _, policy := range f.EgressDeniedBy {
			apiVersion, err := w.policyService.GetNetworkPolicyAPIVersion(policy.Kind, policy.Name, policy.Namespace)
			if err != nil {
				w.log.Error(err, "Failed to get API version for policy", "policyKind", policy.Kind, "policyName", policy.Name, "policyNamespace", policy.Namespace)
			}
			egressPolicies = append(egressPolicies, Policy{
				TypeMeta:  metav1.TypeMeta{APIVersion: apiVersion, Kind: policy.Kind},
				Name:      policy.Name,
				Namespace: policy.Namespace,
			})
		}
	}
	if len(f.IngressDeniedBy) > 0 {
		for _, policy := range f.IngressDeniedBy {
			apiVersion, err := w.policyService.GetNetworkPolicyAPIVersion(policy.Kind, policy.Name, policy.Namespace)
			if err != nil {
				w.log.Error(err, "Failed to get API version for policy", "policyKind", policy.Kind, "policyName", policy.Name, "policyNamespace", policy.Namespace)
			}
			ingressPolicies = append(ingressPolicies, Policy{
				TypeMeta:  metav1.TypeMeta{APIVersion: apiVersion, Kind: policy.Kind},
				Name:      policy.Name,
				Namespace: policy.Namespace,
			})
		}
	}

	// Debug log
	w.log.Info("Policy deny event detected",
		"timestamp", flow.Time.AsTime().Format(time.RFC3339),
		"dropReason", f.DropReasonDesc.String(),
		"protocol", proto,
		"srcNamespace", srcNamespace,
		"srcLabels", strings.Join(srcLabels, ","),
		"srcPod", srcName,
		"srcWorkloads", strings.Join(srcWorkloads, ","),
		"dstNamespace", dstNamespace,
		"dstLabels", strings.Join(dstLabels, ","),
		"dstPod", dstName,
		"dstWorkloads", strings.Join(dstWorkloads, ","),
		"egressDeniedBy", egressPolicies,
		"ingressDeniedBy", ingressPolicies,
	)

	event := &PolicyDenyEvent{
		Timestamp:         flow.Time.AsTime().Unix(),
		CNIType:           string(w.telemetry.cniType),
		Protocol:          corev1.Protocol(proto),
		SrcNamespace:      srcNamespace,
		SrcName:           srcName,
		SrcLabels:         srcLabels,
		DstNamespace:      dstNamespace,
		DstName:           dstName,
		DstLabels:         dstLabels,
		SrcWorkloads:      srcWorkloads,
		DstWorkloads:      dstWorkloads,
		EgressEnforcedBy:  egressPolicies,
		IngressEnforcedBy: ingressPolicies,
	}

	err := w.policyService.ProcessPolicyDenyEvent(event)
	if err != nil {
		return fmt.Errorf("failed to process PolicyDenyEvent: %w", err)
	}

	return nil
}

func (w *CiliumWatcher) Shutdown(ctx context.Context) error {
	if err := w.telemetry.Shutdown(ctx); err != nil {
		w.log.Error(err, "error shutting down OpenTelemetry")
		return err
	}

	if w.conn != nil {
		w.log.Info("Closing Cilium connection")
		return w.conn.Close()
	}

	return nil
}
