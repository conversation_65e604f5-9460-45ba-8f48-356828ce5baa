package cniwatcher

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	flowpb "github.com/cilium/cilium/api/v1/flow"
	hubbleObserver "github.com/cilium/cilium/api/v1/observer"
	monitorApi "github.com/cilium/cilium/pkg/monitor/api"
	"github.com/neuvector/network-enforcement/internal/types"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

const (
	DefaultHubbleEndpoint = "unix:///var/run/cilium/hubble.sock"
)

type CiliumWatcher struct {
	CNIWatcherConfig
	hubbleEndpoint string
	client         hubbleObserver.ObserverClient
	conn           *grpc.ClientConn
}

func NewCiliumWatcher(cfg CNIWatcherConfig) (*CiliumWatcher, error) {
	if cfg.NodeName == "" {
		return nil, fmt.Errorf("NodeName cannot be empty")
	}

	var hubbleEndpoint string
	if endpoint := os.Getenv("HUBBLE_ENDPOINT"); endpoint != "" {
		hubbleEndpoint = endpoint
	} else {
		hubbleEndpoint = DefaultHubbleEndpoint
	}

	watcher := &CiliumWatcher{
		CNIWatcherConfig: cfg,
		hubbleEndpoint:   hubbleEndpoint,
	}

	return watcher, nil
}

func (w *CiliumWatcher) Start() error {
	w.Log.Info("Starting Cilium cniWatcher", "node", w.NodeName, "cniType", w.CNIType)

	retryDelay := 5 * time.Second
	maxRetryDelay := 30 * time.Second

	for {
		select {
		case <-w.Ctx.Done():
			w.Log.Info("Cilium cniWatcher shutting down due to context cancel")
			return nil
		default:
			if err := w.connectToHubble(); err != nil {
				w.Log.Error(err, "Failed to connect to Hubble, will retry")
				time.Sleep(retryDelay)
				retryDelay = time.Duration(float64(retryDelay) * 1.5)
				if retryDelay > maxRetryDelay {
					retryDelay = maxRetryDelay
				}
				continue
			}
			retryDelay = 5 * time.Second

			// Start watching flows (this will block until error or context cancellation)
			if err := w.watchFlows(); err != nil {
				if w.Ctx.Err() == context.Canceled {
					return nil
				}
				w.Log.Error(err, "Error watching flows, will retry connection")
				if w.conn != nil {
					w.conn.Close()
					w.conn = nil
					w.client = nil
				}
				time.Sleep(retryDelay)
				continue
			}
		}
	}
}

func (w *CiliumWatcher) connectToHubble() error {
	conn, err := grpc.NewClient(
		w.hubbleEndpoint,
		grpc.WithTransportCredentials(insecure.NewCredentials()), // TODO: Replace with secure credentials (TLS/mTLS) for production use
	)

	if err != nil {
		return fmt.Errorf("failed to connect to Hubble: %w", err)
	}

	w.conn = conn
	w.client = hubbleObserver.NewObserverClient(conn)
	w.Log.Info("Successfully connected to Hubble", "endpoint", w.hubbleEndpoint)

	return nil
}

func (w *CiliumWatcher) watchFlows() error {
	req := &hubbleObserver.GetFlowsRequest{
		Number: 0,
		Follow: true,
		Whitelist: []*flowpb.FlowFilter{
			{
				EventType: []*flowpb.EventTypeFilter{
					{Type: monitorApi.MessageTypePolicyVerdict},
				},
				DropReasonDesc: []flowpb.DropReason{
					flowpb.DropReason_POLICY_DENIED,
					flowpb.DropReason_POLICY_DENY,
				},
			},
		},
	}

	w.Log.Info("Starting to watch Cilium flows from Hubble", "filter", "DROPPED events with policy deny reasons")
	client, err := w.client.GetFlows(w.Ctx, req)
	if err != nil {
		return fmt.Errorf("failed to get flows from Hubble: %w", err)
	}

	for {
		select {
		case <-w.Ctx.Done():
			w.Log.Info("Cilium cniWatcher shutting down due to context cancel")
			return nil
		default:
			flow, err := client.Recv()
			if err != nil {
				if err.Error() == "EOF" {
					continue
				}
				return fmt.Errorf("error receiving flow from Hubble: %w", err)
			}

			if err := w.parsePolicyDenyEvent(flow); err != nil {
				w.Log.Error(err, "failed to parse policy deny event", "flow", flow)
			}
		}
	}
}

func (w *CiliumWatcher) parsePolicyDenyEvent(flow *hubbleObserver.GetFlowsResponse) error {
	if flow == nil {
		return fmt.Errorf("flow is empty")
	}

	flowResponse, ok := flow.ResponseTypes.(*hubbleObserver.GetFlowsResponse_Flow)
	if !ok || flowResponse == nil || flowResponse.Flow == nil {
		return fmt.Errorf("flowResponse is empty")
	}

	f := flowResponse.Flow

	if f.EventType == nil ||
		(f.DropReasonDesc != flowpb.DropReason_POLICY_DENIED &&
			f.DropReasonDesc != flowpb.DropReason_POLICY_DENY) {
		return nil
	}

	var proto string
	var srcNamespace, srcName, dstNamespace, dstName string
	var srcLabels, dstLabels []string
	var srcWorkloads, dstWorkloads []string

	if f.Source != nil {
		srcNamespace = f.Source.Namespace
		srcName = f.Source.PodName
		srcLabels = f.Source.Labels
		if len(f.Source.Workloads) > 0 {
			for _, wl := range f.Source.Workloads {
				if wl.Kind != "" && wl.Name != "" {
					srcWorkloads = append(srcWorkloads, fmt.Sprintf("%s/%s", wl.Kind, wl.Name))
				}
			}
		}
	}

	if f.Destination != nil {
		dstNamespace = f.Destination.Namespace
		dstName = f.Destination.PodName
		dstLabels = f.Destination.Labels
		if len(f.Destination.Workloads) > 0 {
			for _, wl := range f.Destination.Workloads {
				if wl.Kind != "" && wl.Name != "" {
					dstWorkloads = append(dstWorkloads, fmt.Sprintf("%s/%s", wl.Kind, wl.Name))
				}
			}
		}
	}

	if f.L4 != nil {
		switch f.L4.Protocol.(type) {
		case *flowpb.Layer4_TCP:
			proto = "TCP"
		case *flowpb.Layer4_UDP:
			proto = "UDP"
		case *flowpb.Layer4_ICMPv4, *flowpb.Layer4_ICMPv6:
			proto = "ICMP"
		case *flowpb.Layer4_SCTP:
			proto = "SCTP"
		default:
			proto = "Unknown"
		}
	}

	var egressPolicies, ingressPolicies []types.Policy
	if len(f.EgressDeniedBy) > 0 {
		for _, policy := range f.EgressDeniedBy {
			apiVersion, err := w.GetNetworkPolicyAPIVersion(policy.Kind)
			if err != nil {
				w.Log.Error(err, "Failed to get API version for policy", "policyKind", policy.Kind, "policyName", policy.Name, "policyNamespace", policy.Namespace)
			}
			egressPolicies = append(egressPolicies, types.Policy{
				TypeMeta:  metav1.TypeMeta{APIVersion: apiVersion, Kind: policy.Kind},
				Name:      policy.Name,
				Namespace: policy.Namespace,
			})
		}
	}
	if len(f.IngressDeniedBy) > 0 {
		for _, policy := range f.IngressDeniedBy {
			apiVersion, err := w.GetNetworkPolicyAPIVersion(policy.Kind)
			if err != nil {
				w.Log.Error(err, "Failed to get API version for policy", "policyKind", policy.Kind, "policyName", policy.Name, "policyNamespace", policy.Namespace)
			}
			ingressPolicies = append(ingressPolicies, types.Policy{
				TypeMeta:  metav1.TypeMeta{APIVersion: apiVersion, Kind: policy.Kind},
				Name:      policy.Name,
				Namespace: policy.Namespace,
			})
		}
	}

	// For debugging
	w.Log.Info("Policy deny event detected",
		"timestamp", flow.Time.AsTime().Format(time.RFC3339),
		"dropReason", f.DropReasonDesc.String(),
		"protocol", proto,
		"srcNamespace", srcNamespace,
		"srcLabels", strings.Join(srcLabels, ","),
		"srcPod", srcName,
		"srcWorkloads", strings.Join(srcWorkloads, ","),
		"dstNamespace", dstNamespace,
		"dstLabels", strings.Join(dstLabels, ","),
		"dstPod", dstName,
		"dstWorkloads", strings.Join(dstWorkloads, ","),
		"egressDeniedBy", egressPolicies,
		"ingressDeniedBy", ingressPolicies,
	)

	event := &types.PolicyDenyEvent{
		Timestamp:         flow.Time.AsTime().Unix(),
		CNIType:           string(w.CNIType),
		Protocol:          corev1.Protocol(proto),
		SrcNamespace:      srcNamespace,
		SrcName:           srcName,
		SrcLabels:         srcLabels,
		DstNamespace:      dstNamespace,
		DstName:           dstName,
		DstLabels:         dstLabels,
		SrcWorkloads:      srcWorkloads,
		DstWorkloads:      dstWorkloads,
		EgressEnforcedBy:  egressPolicies,
		IngressEnforcedBy: ingressPolicies,
	}

	err := w.ProcessPolicyDenyEvent(event)
	if err != nil {
		return fmt.Errorf("failed to process PolicyDenyEvent: %w", err)
	}

	return nil
}

func (w *CiliumWatcher) Shutdown() error {
	if w.conn != nil {
		return w.conn.Close()
	}

	return nil
}
