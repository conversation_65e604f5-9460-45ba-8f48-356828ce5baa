package cniwatcher

import (
	"context"
	"crypto/tls"
	"fmt"
	"os"
	"path/filepath"
	"time"

	pb "github.com/neuvector/network-enforcement/internal/cniwatcher/calico/goldmane"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

const (
	DefaultGoldmaneEndpoint = "goldmane.calico-system.svc:7443"
)

type CalicoWatcher struct {
	BaseWatcher
	goldmaneEndpoint string
	client           pb.FlowsClient
	conn             *grpc.ClientConn
}

func NewCalicoWatcher(cfg CNIWatcherConfig) (*CalicoWatcher, error) {
	if cfg.NodeName == "" {
		return nil, fmt.Errorf("node name cannot be empty")
	}

	baseWatcher := BaseWatcher{
		clientset: cfg.Clientset,
		config:    cfg.Config,
		log:       cfg.Log,
		nodeName:  cfg.NodeName,
		cniType:   CNITypeCalico,
	}

	var goldmaneEndpoint string
	if endpoint := os.Getenv("GOLDMANE_ENDPOINT"); endpoint != "" {
		goldmaneEndpoint = endpoint
	} else {
		goldmaneEndpoint = DefaultGoldmaneEndpoint
	}

	watcher := &CalicoWatcher{
		BaseWatcher:      baseWatcher,
		goldmaneEndpoint: goldmaneEndpoint,
	}

	if err := watcher.InitOtel(context.Background()); err != nil {
		cfg.Log.Error(err, "Failed to initialize OpenTelemetry, continuing without tracing")
	}

	return watcher, nil
}

func (w *CalicoWatcher) Start(ctx context.Context) error {
	w.log.Info("Starting Calico Watcher", "node", w.nodeName, "cniType", w.cniType)

	doneCh := make(chan struct{})

	go func() {
		defer close(doneCh)
		retryDelay := 5 * time.Second
		maxRetryDelay := 30 * time.Second

		for {
			select {
			case <-ctx.Done():
				return
			default:
				if w.client == nil {
					if err := w.connectToGoldmane(); err != nil {
						w.log.Error(err, "Failed to connect to Goldmane, will retry")
						time.Sleep(retryDelay)
						retryDelay = time.Duration(float64(retryDelay) * 1.5)
						if retryDelay > maxRetryDelay {
							retryDelay = maxRetryDelay
						}
						continue
					}
					retryDelay = 5 * time.Second
				}

				if err := w.watchFlows(ctx); err != nil {
					if ctx.Err() == context.Canceled {
						return
					}
					w.log.Error(err, "Error watching flows, will retry")
					if w.conn != nil {
						w.conn.Close()
						w.conn = nil
						w.client = nil
					}
					time.Sleep(retryDelay)
					continue
				}
			}
		}
	}()

	<-ctx.Done()
	return w.Shutdown(ctx)
}

func (w *CalicoWatcher) connectToGoldmane() error {
	maxRetries := 3
	retryDelay := 5 * time.Second

	certDir := "/etc/goldmane/certs"
	clientCertPath := filepath.Join(certDir, "tls.crt")
	clientKeyPath := filepath.Join(certDir, "tls.key")

	var conn *grpc.ClientConn
	var connErr error

	if _, err := os.Stat(certDir); err == nil {
		clientCert, err := os.ReadFile(clientCertPath)
		if err != nil {
			return fmt.Errorf("failed to read client certificate: %w", err)
		}

		clientKey, err := os.ReadFile(clientKeyPath)
		if err != nil {
			return fmt.Errorf("failed to read client key: %w", err)
		}

		cert, err := tls.X509KeyPair(clientCert, clientKey)
		if err != nil {
			return fmt.Errorf("failed to load client certificate and key: %w", err)
		}

		tlsConfig := &tls.Config{
			Certificates:       []tls.Certificate{cert},
			InsecureSkipVerify: true, // TODO: TODO: Replace with secure credentials (TLS/mTLS) for production use
		}

		creds := credentials.NewTLS(tlsConfig)
		w.log.Info("Using TLS credentials for Goldmane connection")

		for i := range maxRetries {
			conn, connErr = grpc.NewClient(
				w.goldmaneEndpoint,
				grpc.WithTransportCredentials(creds),
			)

			if connErr == nil {
				break
			}

			w.log.Error(err, "Failed to connect to Goldmane with TLS, retrying",
				"attempt", i+1,
				"maxRetries", maxRetries,
				"endpoint", w.goldmaneEndpoint)

			time.Sleep(retryDelay)
		}
	}

	if connErr != nil || conn == nil {
		return fmt.Errorf("failed to connect to Hubble after %d attempts: %w", maxRetries, connErr)
	}

	w.conn = conn
	w.client = pb.NewFlowsClient(conn)
	w.log.Info("Successfully connected to Goldmane", "endpoint", w.goldmaneEndpoint)

	return nil
}

func (w *CalicoWatcher) watchFlows(ctx context.Context) error {
	filter := &pb.Filter{
		Actions: []pb.Action{pb.Action_Deny},
	}

	req := &pb.FlowStreamRequest{
		StartTimeGte:        0,
		Filter:              filter,
		AggregationInterval: 15, // 15 seconds is the required value
	}

	w.log.Info("Starting to watch Calico policy deny events from Goldmane", "filter", "Action: Deny")
	stream, err := w.client.Stream(ctx, req)
	if err != nil {
		return fmt.Errorf("failed to stream flows from Goldmane: %w", err)
	}

	for {
		select {
		case <-ctx.Done():
			return nil
		default:
			flowResult, err := stream.Recv()
			if err != nil {
				return fmt.Errorf("error receiving flow from Goldmane: %w", err)
			}

			err = w.parsePolicyDenyEvent(flowResult)
			if err != nil {
				w.log.Error(err, "failed to parse policy deny event", "flowResult", flowResult)
			}
		}
	}
}

func (w *CalicoWatcher) parsePolicyDenyEvent(flowResult *pb.FlowResult) error {
	if flowResult == nil || flowResult.Flow == nil || flowResult.Flow.Key == nil {
		return fmt.Errorf("flow is empty")
	}

	flow := flowResult.Flow
	key := flow.Key

	if key.Action != pb.Action_Deny {
		return nil
	}

	// For debugging
	w.log.Info("Flow details",
		"startTime", flow.StartTime,
		"sourceName", key.SourceName,
		"sourceNamespace", key.SourceNamespace,
		"sourceType", key.SourceType.String(),
		"sourceLabels", flow.SourceLabels,
		"destName", key.DestName,
		"destNamespace", key.DestNamespace,
		"destType", key.DestType.String(),
		"destPort", key.DestPort,
		"destLabels", flow.DestLabels,
		"proto", key.Proto,
		"action", key.Action.String(),
		"reporter", key.Reporter.String(),
		"policies", key.Policies)

	var egressPolicies, ingressPolicies []Policy
	if key.Policies != nil {
		if len(key.Policies.EnforcedPolicies) > 0 {
			for _, policy := range key.Policies.EnforcedPolicies {
				policyKind := policy.Kind
				policyName := policy.Name
				policyNamespace := policy.Namespace

				if policy.Name == "" && policy.Trigger != nil {
					policyKind = policy.Trigger.Kind
					policyName = policy.Trigger.Name
					policyNamespace = policy.Trigger.Namespace
				}

				apiVersion, err := w.getNetworkPolicyAPIVersion(policyKind.String(), policyName, policyNamespace)
				if err != nil {
					w.log.Error(err, "Failed to get API version for policy", "policyKind", policyKind.String(), "policyName", policyName, "policyNamespace", policyNamespace)
				}
				p := Policy{
					TypeMeta:  metav1.TypeMeta{APIVersion: apiVersion, Kind: policyKind.String()},
					Name:      policyName,
					Namespace: policyNamespace,
				}

				if key.Reporter == pb.Reporter_Src {
					egressPolicies = append(egressPolicies, p)
				} else if key.Reporter == pb.Reporter_Dst {
					ingressPolicies = append(ingressPolicies, p)
				}
			}
		}
	}

	event := &PolicyDenyEvent{
		Timestamp:         flow.StartTime,
		CNIType:           string(w.cniType),
		Protocol:          corev1.Protocol(key.Proto),
		SrcNamespace:      key.SourceNamespace,
		SrcName:           key.SourceName,
		SrcLabels:         flow.SourceLabels,
		DstNamespace:      key.DestNamespace,
		DstName:           key.DestName,
		DstLabels:         flow.DestLabels,
		EgressEnforcedBy:  egressPolicies,
		IngressEnforcedBy: ingressPolicies,
	}

	err := w.processPolicyDenyEvent(event)
	if err != nil {
		return fmt.Errorf("failed to process PolicyDenyEvent: %w", err)
	}

	return nil
}

func (w *CalicoWatcher) Shutdown(ctx context.Context) error {
	if err := w.ShutdownOtel(ctx); err != nil {
		w.log.Error(err, "error shutting down OpenTelemetry")
	}

	if w.conn != nil {
		w.log.Info("Closing Goldmane connection")
		return w.conn.Close()
	}

	return nil
}
