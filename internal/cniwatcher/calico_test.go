package cniwatcher

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"testing"
	"time"

	pb "github.com/neuvector/network-enforcement/internal/cniwatcher/calico/goldmane"
	"github.com/neuvector/network-enforcement/internal/otel"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc"
	"k8s.io/client-go/kubernetes/fake"
	"k8s.io/client-go/rest"
)

func TestNewCalicoWatcher(t *testing.T) {
	tests := []struct {
		name             string
		goldmaneEnv      string
		expectedEndpoint string
	}{
		{
			name:             "Default Goldmane endpoint",
			goldmaneEnv:      "",
			expectedEndpoint: DefaultGoldmaneEndpoint,
		},
		{
			name:             "Custom Goldmane endpoint",
			goldmaneEnv:      "custom-goldmane:7443",
			expectedEndpoint: "custom-goldmane:7443",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.goldmaneEnv != "" {
				os.Setenv("GOLDMANE_ENDPOINT", tt.goldmaneEnv)
				defer os.Unsetenv("GOLDMANE_ENDPOINT")
			}

			fakeClient := fake.NewSimpleClientset()
			config := &rest.Config{}
			log := logrus.New()
			otelService := otel.NewOpenTelemetryService(context.Background(), log)

			cfg := CNIWatcherConfig{
				Ctx:         context.Background(),
				Clientset:   fakeClient,
				Config:      config,
				Log:         log,
				NodeName:    "test-node",
				CNIType:     CNITypeCalico,
				OtelService: otelService,
			}
			watcher, err := NewCalicoWatcher(cfg)
			assert.NoError(t, err)
			assert.NotNil(t, watcher)
			assert.Equal(t, tt.expectedEndpoint, watcher.goldmaneEndpoint)
		})
	}
}

type TestCalicoWatcher struct {
	*CalicoWatcher
	connectFunc func() error
}

func (w *TestCalicoWatcher) connectToGoldmane() error {
	if w.connectFunc != nil {
		return w.connectFunc()
	}
	return w.CalicoWatcher.connectToGoldmane()
}

func TestCalicoWatcher_ConnectToGoldmane(t *testing.T) {
	tmpDir, err := os.MkdirTemp("", "test-certs")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tmpDir)

	certFiles := []string{"tls.crt", "tls.key"}
	for _, file := range certFiles {
		if err := os.WriteFile(filepath.Join(tmpDir, file), []byte("dummy cert"), 0644); err != nil {
			t.Fatalf("Failed to create cert file %s: %v", file, err)
		}
	}

	clientset := fake.NewSimpleClientset()
	log := logrus.New()
	otelService := otel.NewOpenTelemetryService(context.Background(), log)

	tests := []struct {
		name           string
		certDir        string
		expectedError  bool
		expectedClient bool
	}{
		{
			name:           "Valid certificate directory",
			certDir:        tmpDir,
			expectedError:  false,
			expectedClient: true,
		},
		{
			name:           "Invalid certificate directory",
			certDir:        "/nonexistent/dir",
			expectedError:  true,
			expectedClient: false,
		},
		{
			name:           "Empty certificate directory",
			certDir:        "",
			expectedError:  true,
			expectedClient: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			watcher := &CalicoWatcher{
				CNIWatcherConfig: CNIWatcherConfig{
					Clientset:   clientset,
					NodeName:    "test-node",
					Log:         log,
					OtelService: otelService,
				},
				goldmaneEndpoint: "localhost:4244",
			}

			if tt.certDir != "" {
				os.Setenv("GOLDMANE_CERT_DIR", tt.certDir)
				defer os.Unsetenv("GOLDMANE_CERT_DIR")
			}

			testWatcher := &TestCalicoWatcher{
				CalicoWatcher: watcher,
				connectFunc: func() error {
					if tt.expectedError {
						return fmt.Errorf("connection failed")
					}
					watcher.client = &MockFlowsClient{}
					return nil
				},
			}

			err := testWatcher.connectToGoldmane()

			if tt.expectedError {
				assert.Error(t, err)
				assert.Nil(t, testWatcher.client)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, testWatcher.client)
			}
		})
	}
}

type MockFlowsClient struct {
	pb.FlowsClient
	streamFunc func(ctx context.Context, req *pb.FlowStreamRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[pb.FlowResult], error)
}

func (m *MockFlowsClient) Stream(ctx context.Context, req *pb.FlowStreamRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[pb.FlowResult], error) {
	if m.streamFunc != nil {
		return m.streamFunc(ctx, req, opts...)
	}
	return nil, fmt.Errorf("stream function not set")
}

type MockStreamClient struct {
	grpc.ServerStreamingClient[pb.FlowResult]
	recvFunc func() (*pb.FlowResult, error)
}

func (m *MockStreamClient) Recv() (*pb.FlowResult, error) {
	if m.recvFunc != nil {
		return m.recvFunc()
	}
	return nil, fmt.Errorf("recv function not set")
}

func TestCalicoWatcher_WatchFlows(t *testing.T) {
	clientset := fake.NewSimpleClientset()
	log := logrus.New()
	otelService := otel.NewOpenTelemetryService(context.Background(), log)

	watcher := &CalicoWatcher{
		CNIWatcherConfig: CNIWatcherConfig{
			Clientset:   clientset,
			NodeName:    "test-node",
			Log:         log,
			OtelService: otelService,
		},
		goldmaneEndpoint: "localhost:4244",
	}

	tests := []struct {
		name          string
		streamFunc    func(ctx context.Context, req *pb.FlowStreamRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[pb.FlowResult], error)
		recvFunc      func() (*pb.FlowResult, error)
		expectedError bool
	}{
		{
			name: "Successful flow stream",
			streamFunc: func(ctx context.Context, req *pb.FlowStreamRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[pb.FlowResult], error) {
				return &MockStreamClient{
					recvFunc: func() (*pb.FlowResult, error) {
						return &pb.FlowResult{
							Flow: &pb.Flow{
								Key: &pb.FlowKey{
									Action: pb.Action_Deny,
								},
							},
						}, nil
					},
				}, nil
			},
			recvFunc: func() (*pb.FlowResult, error) {
				return &pb.FlowResult{
					Flow: &pb.Flow{
						Key: &pb.FlowKey{
							Action: pb.Action_Deny,
						},
					},
				}, nil
			},
			expectedError: false,
		},
		{
			name: "Stream error",
			streamFunc: func(ctx context.Context, req *pb.FlowStreamRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[pb.FlowResult], error) {
				return nil, fmt.Errorf("stream error")
			},
			recvFunc:      nil,
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			watcher.client = &MockFlowsClient{
				streamFunc: tt.streamFunc,
			}

			ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
			defer cancel()
			watcher.Ctx = ctx

			err := watcher.watchFlows()
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				// watchFlows will likely timeout due to context cancellation, which is expected
				assert.NoError(t, err)
			}
		})
	}
}

func TestCalicoWatcher_ParsePolicyDenyEvent(t *testing.T) {
	tests := []struct {
		name          string
		flowResult    *pb.FlowResult
		expectedError bool
	}{
		{
			name: "Valid deny flow",
			flowResult: &pb.FlowResult{
				Flow: &pb.Flow{
					Key: &pb.FlowKey{
						Action: pb.Action_Deny,
					},
				},
			},
			expectedError: false,
		},
		{
			name: "Allow flow (should be ignored)",
			flowResult: &pb.FlowResult{
				Flow: &pb.Flow{
					Key: &pb.FlowKey{
						Action: pb.Action_Allow,
					},
				},
			},
			expectedError: false,
		},
		{
			name:          "Nil flow result",
			flowResult:    nil,
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			log := logrus.New()
			otelService := otel.NewOpenTelemetryService(context.Background(), log)

			watcher := &CalicoWatcher{
				CNIWatcherConfig: CNIWatcherConfig{
					Log:         log,
					OtelService: otelService,
				},
			}

			err := watcher.parsePolicyDenyEvent(tt.flowResult)
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestCalicoWatcher_Shutdown(t *testing.T) {
	tests := []struct {
		name    string
		wantErr bool
	}{
		{
			name:    "Successful shutdown",
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			log := logrus.New()
			otelService := otel.NewOpenTelemetryService(context.Background(), log)

			watcher := &CalicoWatcher{
				CNIWatcherConfig: CNIWatcherConfig{
					Log:         log,
					OtelService: otelService,
				},
			}

			err := watcher.Shutdown()
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
