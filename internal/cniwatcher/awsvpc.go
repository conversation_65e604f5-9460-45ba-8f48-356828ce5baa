package cniwatcher

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/jdrews/go-tailer/fswatcher"
	"github.com/jdrews/go-tailer/glob"
	"github.com/neuvector/network-enforcement/internal/types"
	corev1 "k8s.io/api/core/v1"
)

const (
	awsVPCLogPath = "/var/log/aws-routed-eni/network-policy-agent.log"
)

type AWSVPCWatcher struct {
	CNIWatcherConfig
	tailer fswatcher.FileTailer
}

type flowLog struct {
	Level     string `json:"level"`
	Timestamp string `json:"ts"`
	Logger    string `json:"logger"`
	Message   string `json:"msg"`
	SrcIP     string `json:"Src IP"`
	SrcPort   int    `json:"Src Port"`
	DestIP    string `json:"Dest IP"`
	DestPort  int    `json:"Dest Port"`
	Proto     string `json:"Proto"`
	Verdict   string `json:"Verdict"`
}

func NewAWSVPCWatcher(cfg CNIWatcherConfig) (*AWSVPCWatcher, error) {
	if cfg.NodeName == "" {
		return nil, fmt.Errorf("NodeName cannot be empty")
	}
	return &AWSVPCWatcher{CNIWatcherConfig: cfg}, nil
}

func (w *AWSVPCWatcher) Start() error {
	w.Log.Info("Starting AWS VPC cniWatcher", "node", w.NodeName, "cniType", w.CNIType)

	parsedGlob, err := glob.Parse(awsVPCLogPath)
	if err != nil {
		return fmt.Errorf("failed to parse log path: %w", err)
	}

	tailer, err := fswatcher.RunFileTailer([]glob.Glob{parsedGlob}, false, true, w.Log)
	if err != nil {
		return fmt.Errorf("failed to start file tailer: %w", err)
	}

	w.tailer = tailer

	for {
		select {
		case <-w.Ctx.Done():
			w.Log.Info("AWSVPC cniWatcher shutting down due to context cancel")
			return nil
		case line := <-tailer.Lines():
			if line.Line == "" {
				continue
			}
			if err := w.parsePolicyDenyEvent(line.Line); err != nil {
				w.Log.Error(err, "failed to parse policy deny event", "line", line.Line)
			}
		}
	}
}

func (w *AWSVPCWatcher) parsePolicyDenyEvent(line string) error {
	var flow flowLog
	if err := json.Unmarshal([]byte(line), &flow); err != nil {
		return fmt.Errorf("failed to unmarshal log line: %w", err)
	}

	if flow.Verdict != "DENY" {
		return nil
	}

	timestamp, err := time.Parse(time.RFC3339Nano, flow.Timestamp)
	if err != nil {
		w.Log.Error(err, "failed to parse timestamp", "timestamp", flow.Timestamp)
		timestamp = time.Now()
	}

	srcEndpointInfo, err := w.ResolvePodOrServiceByIP(flow.SrcIP)
	if err != nil {
		w.Log.Error(err, "failed to get source endpoint info", "ip", flow.SrcIP)
	}

	dstEndpointInfo, err := w.ResolvePodOrServiceByIP(flow.DestIP)
	if err != nil {
		w.Log.Error(err, "failed to get destination endpoint info", "ip", flow.DestIP)
	}

	event := &types.PolicyDenyEvent{
		Timestamp:    timestamp.Unix(),
		CNIType:      string(w.CNIType),
		Protocol:     corev1.Protocol(flow.Proto),
		SrcNamespace: srcEndpointInfo.Namespace,
		SrcName:      srcEndpointInfo.Name,
		SrcLabels:    srcEndpointInfo.Labels,
		DstNamespace: dstEndpointInfo.Namespace,
		DstName:      dstEndpointInfo.Name,
		DstLabels:    dstEndpointInfo.Labels,
	}

	err = w.ProcessPolicyDenyEvent(event)
	if err != nil {
		return fmt.Errorf("failed to process PolicyDenyEvent: %w", err)
	}

	return nil
}

func (w *AWSVPCWatcher) Shutdown() error {
	if w.tailer != nil {
		w.tailer.Close()
	}

	return nil
}
