package cniwatcher

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/jdrews/go-tailer/fswatcher"
	"github.com/jdrews/go-tailer/glob"
	"github.com/sirupsen/logrus"
	corev1 "k8s.io/api/core/v1"
)

const (
	awsVPCLogPath = "/var/log/aws-routed-eni/network-policy-agent.log"
)

type AWSVPCWatcher struct {
	log           *logrus.Logger
	telemetry     *OpenTelemetryService
	policyService *PolicyEventService
}

type flowLog struct {
	Level     string `json:"level"`
	Timestamp string `json:"ts"`
	Logger    string `json:"logger"`
	Message   string `json:"msg"`
	SrcIP     string `json:"Src IP"`
	SrcPort   int    `json:"Src Port"`
	DestIP    string `json:"Dest IP"`
	DestPort  int    `json:"Dest Port"`
	Proto     string `json:"Proto"`
	Verdict   string `json:"Verdict"`
}

func NewAWSVPCWatcher(cfg CNIWatcherConfig) (*AWSVPCWatcher, error) {
	if cfg.NodeName == "" {
		return nil, fmt.Errorf("node name cannot be empty")
	}

	telemetry := NewOpenTelemetryService(cfg.Log, cfg.NodeName, CNITypeAWSVPC)
	policyService := NewPolicyEventService(cfg.Clientset, cfg.Config, cfg.Log, telemetry)

	watcher := &AWSVPCWatcher{
		log:           cfg.Log,
		telemetry:     telemetry,
		policyService: policyService,
	}

	if err := telemetry.Init(context.Background()); err != nil {
		cfg.Log.Error(err, "Failed to initialize OpenTelemetry, continuing without tracing")
	}

	return watcher, nil
}

func (w *AWSVPCWatcher) Start(ctx context.Context) error {
	w.log.Info("Starting AWS VPC watcher", "node", w.telemetry.nodeName, "cniType", w.telemetry.cniType)

	parsedGlob, err := glob.Parse(awsVPCLogPath)
	if err != nil {
		return fmt.Errorf("failed to parse log path: %w", err)
	}

	tailer, err := fswatcher.RunFileTailer([]glob.Glob{parsedGlob}, false, true, w.log)
	if err != nil {
		return fmt.Errorf("failed to start tailer: %w", err)
	}

	for {
		select {
		case <-ctx.Done():
			return nil
		case line := <-tailer.Lines():
			if line.Line == "" {
				continue
			}
			if err := w.parsePolicyDenyEvent(line.Line); err != nil {
				w.log.Error(err, "failed to parse policy deny event", "line", line.Line)
			}
		}
	}
}

func (w *AWSVPCWatcher) parsePolicyDenyEvent(line string) error {
	var flow flowLog
	if err := json.Unmarshal([]byte(line), &flow); err != nil {
		return fmt.Errorf("failed to unmarshal log line: %w", err)
	}

	if flow.Verdict != "DENY" {
		return nil
	}

	timestamp, err := time.Parse(time.RFC3339Nano, flow.Timestamp)
	if err != nil {
		w.log.Error(err, "failed to parse timestamp", "timestamp", flow.Timestamp)
		timestamp = time.Now()
	}

	srcEndpointInfo, err := w.policyService.GetEndpointInfoByIP(flow.SrcIP)
	if err != nil {
		w.log.Error(err, "failed to get source endpoint info", "ip", flow.SrcIP)
	}

	dstEndpointInfo, err := w.policyService.GetEndpointInfoByIP(flow.DestIP)
	if err != nil {
		w.log.Error(err, "failed to get destination endpoint info", "ip", flow.DestIP)
	}

	event := &PolicyDenyEvent{
		Timestamp:    timestamp.Unix(),
		CNIType:      string(w.telemetry.cniType),
		Protocol:     corev1.Protocol(flow.Proto),
		SrcNamespace: srcEndpointInfo.Namespace,
		SrcName:      srcEndpointInfo.Name,
		SrcLabels:    srcEndpointInfo.Labels,
		DstNamespace: dstEndpointInfo.Namespace,
		DstName:      dstEndpointInfo.Name,
		DstLabels:    dstEndpointInfo.Labels,
	}

	err = w.policyService.ProcessPolicyDenyEvent(event)
	if err != nil {
		return fmt.Errorf("failed to process PolicyDenyEvent: %w", err)
	}

	return nil
}

func (w *AWSVPCWatcher) Shutdown(ctx context.Context) error {
	w.log.Info("Shutting down AWS VPC cniWatcher")

	if err := w.telemetry.Shutdown(ctx); err != nil {
		w.log.Error(err, "error shutting down OpenTelemetry")
		return err
	}

	return nil
}
