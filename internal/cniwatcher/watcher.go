package cniwatcher

import (
	"context"
	"fmt"
	"net"
	"time"

	"github.com/neuvector/network-enforcement/internal/types"
	"github.com/neuvector/network-enforcement/internal/otel"
	"github.com/sirupsen/logrus"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
)

type CNIType string

const (
	CNITypeAWSVPC  CNIType = "aws-vpc"
	CNITypeCalico  CNIType = "calico"
	CNITypeCilium  CNIType = "cilium"
	CNITypeFlannel CNIType = "flannel"
	CNITypeUnknown CNIType = "unknown"
)

type PodOrServiceInfo struct {
	Name      string
	Namespace string
	// "service", "external-service", "pod"
	Type   string
	Labels []string
}

type CNIWatcher interface {
	Start() error
	Shutdown() error
}

type CNIWatcherConfig struct {
	Ctx         context.Context
	Clientset   kubernetes.Interface
	Config      *rest.Config
	Log         *logrus.Logger
	NodeName    string
	CNIType     CNIType
	OtelService *otel.OtelService
}

func NewCNIWatcher(cfg CNIWatcherConfig) (CNIWatcher, error) {
	if cfg.NodeName == "" {
		return nil, fmt.Errorf("NodeName must be set")
	}
	if cfg.CNIType == "" || cfg.CNIType == CNITypeUnknown {
		return nil, fmt.Errorf("CNIType must be set and known")
	}
	switch cfg.CNIType {
	case CNITypeAWSVPC:
		return NewAWSVPCWatcher(cfg)
	case CNITypeCalico:
		return NewCalicoWatcher(cfg)
	case CNITypeCilium:
		return NewCiliumWatcher(cfg)
	case CNITypeFlannel:
		return NewFlannelWatcher(cfg)
	default:
		return nil, fmt.Errorf("unsupported or unknown CNI type: %q", cfg.CNIType)
	}
}

func (c *CNIWatcherConfig) ProcessPolicyDenyEvent(event *types.PolicyDenyEvent) error {
	if event == nil {
		c.Log.Warn("event is empty")
		return nil
	}

	if c.OtelService != nil {
		c.OtelService.Service.Log.Info("Processing policy deny event",
			"timestamp", event.Timestamp,
			"nodeName", event.NodeName,
			"cniPlugin", event.CNIType,
			"protocol", event.Protocol,
			"srcNamespace", event.SrcNamespace,
			"srcName", event.SrcName,
			"srcLabels", event.SrcLabels,
			"srcWorkloads", event.SrcWorkloads,
			"dstNamespace", event.DstNamespace,
			"dstName", event.DstName,
			"dstLabels", event.DstLabels,
			"dstWorkloads", event.DstWorkloads,
			"egressEnforcedBy", event.EgressEnforcedBy,
			"ingressEnforcedBy", event.IngressEnforcedBy)

		c.OtelService.EmitPolicyDenyEvent(event)
	}

	return nil
}

func (c *CNIWatcherConfig) GetNetworkPolicyAPIVersion(kind string) (string, error) {
	var group, version string
	switch kind {
	case "NetworkPolicy":
		group = "networking.k8s.io"
		version = "v1"
	case "CalicoNetworkPolicy", "GlobalNetworkPolicy":
		group = "projectcalico.org"
		version = "v3"
	case "CiliumNetworkPolicy", "CiliumClusterwideNetworkPolicy":
		group = "cilium.io"
		version = "v2"
	default:
		return "", fmt.Errorf("unsupported network policy kind: %s", kind)
	}

	return fmt.Sprintf("%s/%s", group, version), nil
}

// AWSVPC CNI and Fallnel don't have the Pod or Service info(i.e. name, namespace, labels) in the policy deny log.
// This Method is specifically about resolving a Pod or Service info from an IP address
func (c *CNIWatcherConfig) ResolvePodOrServiceByIP(ip string) (PodOrServiceInfo, error) {
	if ip == "" {
		return PodOrServiceInfo{}, fmt.Errorf("IP address cannot be empty")
	}

	if net.ParseIP(ip) == nil {
		return PodOrServiceInfo{}, fmt.Errorf("invalid IP address format: %s", ip)
	}

	// Check for services with cluster IP
	services, err := c.Clientset.CoreV1().Services("").List(c.Ctx, metav1.ListOptions{
		FieldSelector: fmt.Sprintf("spec.clusterIP=%s", ip),
	})
	if err == nil && len(services.Items) > 0 {
		return PodOrServiceInfo{
			Name:      services.Items[0].Name,
			Namespace: services.Items[0].Namespace,
			Type:      "service",
		}, nil
	}

	// Check for services with external IPs
	if services, err := c.Clientset.CoreV1().Services("").List(c.Ctx, metav1.ListOptions{}); err == nil {
		for _, svc := range services.Items {
			for _, extIP := range svc.Spec.ExternalIPs {
				if extIP == ip {
					return PodOrServiceInfo{
						Name:      svc.Name,
						Namespace: svc.Namespace,
						Type:      "external-service",
					}, nil
				}
			}
		}
	}

	// Check for pods
	pods, err := c.Clientset.CoreV1().Pods("").List(c.Ctx, metav1.ListOptions{
		FieldSelector: fmt.Sprintf("status.podIP=%s", ip),
	})
	if err != nil {
		return PodOrServiceInfo{}, fmt.Errorf("failed to list pods: %w", err)
	}

	if len(pods.Items) == 0 {
		return PodOrServiceInfo{}, fmt.Errorf("no endpoint found for IP: %s", ip)
	}

	pod := pods.Items[0]
	labels := make([]string, 0, len(pod.Labels))
	for k, v := range pod.Labels {
		labels = append(labels, fmt.Sprintf("%s=%s", k, v))
	}

	return PodOrServiceInfo{
		Name:      pod.Name,
		Namespace: pod.Namespace,
		Type:      "pod",
		Labels:    labels,
	}, nil
}
