package cniwatcher

import (
	"context"
	"fmt"
	"net"

	"github.com/sirupsen/logrus"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
)

type CNIType string

const (
	CNITypeAWSVPC  CNIType = "aws-vpc"
	CNITypeCalico  CNIType = "calico"
	CNITypeCilium  CNIType = "cilium"
	CNITypeFlannel CNIType = "flannel"
	CNITypeUnknown CNIType = "unknown"
)

type EndpointInfo struct {
	Name      string
	Namespace string
	Labels    []string
}

type Policy struct {
	metav1.TypeMeta `json:",inline"`
	Name            string `json:"name"`
	Namespace       string `json:"namespace"`
}

func (p Policy) String() string {
	return fmt.Sprintf("%s/%s/%s/%s", p.<PERSON>, p.Kind, p.Namespace, p.Name)
}

type PolicyDenyEvent struct {
	Timestamp int64 `json:"timestamp"`
	// e.g. "aws-vpc", "calico", "cilium", "flannel"
	CNIType string `json:"cni_type"`
	// "TCP", "UDP", "ICMP", "SCTP"
	Protocol     corev1.Protocol `json:"protocol"`
	SrcNamespace string          `json:"source_namespace"`
	SrcName      string          `json:"source_name"`
	SrcLabels    []string        `json:"source_labels"`
	DstNamespace string          `json:"destination_namespace"`
	DstName      string          `json:"destination_name"`
	DstLabels    []string        `json:"destination_labels"`
	SrcWorkloads []string        `json:"source_workloads,omitempty"`
	DstWorkloads []string        `json:"destination_workloads,omitempty"`
	// The K8s NetworkPolicies or CiliumNetworkPolicies denying the egress of the flow
	EgressEnforcedBy []Policy `json:"egress_enforced_by,omitempty"`
	// The K8s NetworkPolicies or CiliumNetworkPolicies denying the ingress of the flow
	IngressEnforcedBy []Policy `json:"ingress_enforced_by,omitempty"`
}

type CNIWatcher interface {
	Start(ctx context.Context) error
	Shutdown(ctx context.Context) error
}

type PolicyEventService struct {
	clientset   kubernetes.Interface
	config      *rest.Config
	log         *logrus.Logger
	otelService *OpenTelemetryService
}

type CNIWatcherConfig struct {
	Clientset kubernetes.Interface
	Config    *rest.Config
	Log       *logrus.Logger
	NodeName  string
	CNIType   CNIType
	otelService *OpenTelemetryService
}

func NewCNIWatcher(cfg CNIWatcherConfig) (CNIWatcher, error) {
	if cfg.NodeName == "" {
		return nil, fmt.Errorf("NodeName must be set")
	}
	if cfg.CNIType == "" || cfg.CNIType == CNITypeUnknown {
		return nil, fmt.Errorf("CNIType must be set and known")
	}
	switch cfg.CNIType {
	case CNITypeAWSVPC:
		return NewAWSVPCWatcher(cfg)
	case CNITypeCalico:
		return NewCalicoWatcher(cfg)
	case CNITypeCilium:
		return NewCiliumWatcher(cfg)
	case CNITypeFlannel:
		return NewFlannelWatcher(cfg)
	default:
		return nil, fmt.Errorf("unsupported or unknown CNI type: %q", cfg.CNIType)
	}
}

func (p *PolicyEventService) ProcessPolicyDenyEvent(event *PolicyDenyEvent) error {
	if event == nil {
		return fmt.Errorf("event is empty")
	}

	p.log.Info("Processing policy deny event",
		"timestamp", event.Timestamp,
		"cniPlugin", event.CNIType,
		"protocol", event.Protocol,
		"srcNamespace", event.SrcNamespace,
		"srcName", event.SrcName,
		"srcLabels", event.SrcLabels,
		"srcWorkloads", event.SrcWorkloads,
		"dstNamespace", event.DstNamespace,
		"dstName", event.DstName,
		"dstLabels", event.DstLabels,
		"dstWorkloads", event.DstWorkloads,
		"egressEnforcedBy", event.EgressEnforcedBy,
		"ingressEnforcedBy", event.IngressEnforcedBy)

	if p.otelService != nil {
		p.otelService.EmitPolicyDenyEvent(event)
	}

	return nil
}

func (p *PolicyEventService) GetNetworkPolicyAPIVersion(kind, name, namespace string) (string, error) {
	dynamicClient, err := dynamic.NewForConfig(p.config)
	if err != nil {
		return "", fmt.Errorf("failed to create dynamic client: %w", err)
	}

	var gvr schema.GroupVersionResource
	switch kind {
	case "NetworkPolicy":
		gvr = schema.GroupVersionResource{
			Group:    "networking.k8s.io",
			Version:  "v1",
			Resource: "networkpolicies",
		}
	case "CalicoNetworkPolicy":
		gvr = schema.GroupVersionResource{
			Group:    "projectcalico.org",
			Version:  "v3",
			Resource: "networkpolicies",
		}
	case "GlobalNetworkPolicy":
		gvr = schema.GroupVersionResource{
			Group:    "projectcalico.org",
			Version:  "v3",
			Resource: "globalnetworkpolicies",
		}
	case "CiliumNetworkPolicy":
		gvr = schema.GroupVersionResource{
			Group:    "cilium.io",
			Version:  "v2",
			Resource: "ciliumnetworkpolicies",
		}
	case "CiliumClusterwideNetworkPolicy":
		gvr = schema.GroupVersionResource{
			Group:    "cilium.io",
			Version:  "v2",
			Resource: "ciliumclusterwidenetworkpolicies",
		}
	default:
		return "", fmt.Errorf("unsupported network policy kind: %s", kind)
	}

	_, err = dynamicClient.Resource(gvr).Namespace(namespace).Get(context.Background(), name, metav1.GetOptions{})
	if err != nil {
		return "", fmt.Errorf("failed to get policy: %w", err)
	}

	return fmt.Sprintf("%s/%s", gvr.Group, gvr.Version), nil
}

func policiesToStrings(policies []Policy) []string {
	result := make([]string, len(policies))
	for i, p := range policies {
		result[i] = p.String()
	}
	return result
}

func (p *PolicyEventService) GetEndpointInfoByIP(ip string) (EndpointInfo, error) {
	if ip == "" {
		return EndpointInfo{}, fmt.Errorf("IP address cannot be empty")
	}

	if net.ParseIP(ip) == nil {
		return EndpointInfo{}, fmt.Errorf("invalid IP address format: %s", ip)
	}

	// Check for services with cluster IP
	services, err := p.clientset.CoreV1().Services("").List(context.Background(), metav1.ListOptions{
		FieldSelector: fmt.Sprintf("spec.clusterIP=%s", ip),
	})
	if err == nil && len(services.Items) > 0 {
		return EndpointInfo{
			Name:      services.Items[0].Name,
			Namespace: services.Items[0].Namespace,
			Labels:    []string{"type=service"},
		}, nil
	}

	// Check for services with external IPs
	if services, err := p.clientset.CoreV1().Services("").List(context.Background(), metav1.ListOptions{}); err == nil {
		for _, svc := range services.Items {
			for _, extIP := range svc.Spec.ExternalIPs {
				if extIP == ip {
					return EndpointInfo{
						Name:      svc.Name,
						Namespace: svc.Namespace,
						Labels:    []string{"type=external-service"},
					}, nil
				}
			}
		}
	}

	// Check for pods
	pods, err := p.clientset.CoreV1().Pods("").List(context.Background(), metav1.ListOptions{
		FieldSelector: fmt.Sprintf("status.podIP=%s", ip),
	})
	if err != nil {
		return EndpointInfo{}, fmt.Errorf("failed to list pods: %w", err)
	}

	if len(pods.Items) == 0 {
		return EndpointInfo{}, fmt.Errorf("no endpoint found for IP: %s", ip)
	}

	pod := pods.Items[0]
	labels := make([]string, 0, len(pod.Labels))
	for k, v := range pod.Labels {
		labels = append(labels, fmt.Sprintf("%s=%s", k, v))
	}

	return EndpointInfo{
		Name:      pod.Name,
		Namespace: pod.Namespace,
		Labels:    labels,
	}, nil
}
