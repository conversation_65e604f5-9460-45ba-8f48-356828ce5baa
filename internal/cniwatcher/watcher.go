package cniwatcher

import (
	"context"
	"fmt"
	"net"
	"os"
	"time"

	"github.com/sirupsen/logrus"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
	"go.opentelemetry.io/otel/sdk/resource"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.30.0"
	"go.opentelemetry.io/otel/trace"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
)

var DefaultOtelServiceVersion = "dev"
var DefaultOtelCollectorEndpoint = "otel-collector:4317"

type CNIType string

const (
	CNITypeAWSVPC  CNIType = "aws-vpc"
	CNITypeCalico  CNIType = "calico"
	CNITypeCilium  CNIType = "cilium"
	CNITypeFlannel CNIType = "flannel"
	CNITypeUnknown CNIType = "unknown"
)

type EndpointInfo struct {
	Name      string
	Namespace string
	Labels    []string
}

type Policy struct {
	metav1.TypeMeta `json:",inline"`
	Name            string `json:"name"`
	Namespace       string `json:"namespace"`
}

func (p Policy) String() string {
	return fmt.Sprintf("%s/%s/%s/%s", p.APIVersion, p.Kind, p.Namespace, p.Name)
}

type PolicyDenyEvent struct {
	Timestamp int64 `json:"timestamp"`
	// e.g. "aws-vpc", "calico", "cilium", "flannel"
	CNIType string `json:"cni_type"`
	// "TCP", "UDP", "ICMP", "SCTP"
	Protocol     corev1.Protocol `json:"protocol"`
	SrcNamespace string          `json:"source_namespace"`
	SrcName      string          `json:"source_name"`
	SrcLabels    []string        `json:"source_labels"`
	DstNamespace string          `json:"destination_namespace"`
	DstName      string          `json:"destination_name"`
	DstLabels    []string        `json:"destination_labels"`
	SrcWorkloads []string        `json:"source_workloads,omitempty"`
	DstWorkloads []string        `json:"destination_workloads,omitempty"`
	// The K8s NetworkPolicies or CiliumNetworkPolicies denying the egress of the flow
	EgressEnforcedBy []Policy `json:"egress_enforced_by,omitempty"`
	// The K8s NetworkPolicies or CiliumNetworkPolicies denying the ingress of the flow
	IngressEnforcedBy []Policy `json:"ingress_enforced_by,omitempty"`
}

type CNIWatcher interface {
	Start(ctx context.Context) error
	Shutdown(ctx context.Context) error
}

// TelemetryService handles OpenTelemetry operations
type TelemetryService struct {
	tracerProvider *sdktrace.TracerProvider
	tracer         trace.Tracer
	log            *logrus.Logger
	nodeName       string
	cniType        CNIType
}

// EndpointService handles Kubernetes endpoint resolution
type EndpointService struct {
	clientset kubernetes.Interface
	log       *logrus.Logger
}

// EventProcessor handles policy deny event processing
type EventProcessor struct {
	telemetry *TelemetryService
	log       *logrus.Logger
}

// PolicyService handles network policy operations
type PolicyService struct {
	config *rest.Config
	log    *logrus.Logger
}

type CNIWatcherConfig struct {
	Clientset kubernetes.Interface
	Config    *rest.Config
	Log       *logrus.Logger
	NodeName  string
	CNIType   CNIType
}

func NewCNIWatcher(cfg CNIWatcherConfig) (CNIWatcher, error) {
	if cfg.NodeName == "" {
		return nil, fmt.Errorf("NodeName must be set")
	}
	if cfg.CNIType == "" || cfg.CNIType == CNITypeUnknown {
		return nil, fmt.Errorf("CNIType must be set and known")
	}
	switch cfg.CNIType {
	case CNITypeAWSVPC:
		return NewAWSVPCWatcher(cfg)
	case CNITypeCalico:
		return NewCalicoWatcher(cfg)
	case CNITypeCilium:
		return NewCiliumWatcher(cfg)
	case CNITypeFlannel:
		return NewFlannelWatcher(cfg)
	default:
		return nil, fmt.Errorf("unsupported or unknown CNI type: %q", cfg.CNIType)
	}
}

// NewTelemetryService creates a new telemetry service
func NewTelemetryService(log *logrus.Logger, nodeName string, cniType CNIType) *TelemetryService {
	return &TelemetryService{
		log:      log,
		nodeName: nodeName,
		cniType:  cniType,
	}
}

func (t *TelemetryService) Init(ctx context.Context) error {
	collectorEndpoint := os.Getenv("OTEL_EXPORTER_OTLP_ENDPOINT")
	if collectorEndpoint == "" {
		collectorEndpoint = DefaultOtelCollectorEndpoint
	}

	exporter, err := otlptracegrpc.New(ctx,
		otlptracegrpc.WithEndpoint(collectorEndpoint),
		otlptracegrpc.WithInsecure(),
	)
	if err != nil {
		return fmt.Errorf("failed to create OTLP exporter: %w", err)
	}

	res, err := resource.New(ctx,
		resource.WithAttributes(
			semconv.ServiceNameKey.String("cniwatcher"),
			semconv.ServiceVersionKey.String(DefaultOtelServiceVersion),
			attribute.String("node.name", t.nodeName),
			attribute.String("cni.type", string(t.cniType)),
		),
	)
	if err != nil {
		return fmt.Errorf("failed to create resource: %w", err)
	}

	t.tracerProvider = sdktrace.NewTracerProvider(
		sdktrace.WithBatcher(exporter),
		sdktrace.WithResource(res),
	)

	otel.SetTracerProvider(t.tracerProvider)

	t.tracer = t.tracerProvider.Tracer("cniwatcher")

	t.log.Info("OpenTelemetry initialized", "collector", collectorEndpoint)
	return nil
}

func (t *TelemetryService) EmitPolicyDenyEvent(event *PolicyDenyEvent) {
	if t.tracer == nil {
		t.log.Warn("OpenTelemetry is not initialized, skip emitting policy deny event")
		return
	}

	ctx := context.Background()
	_, span := t.tracer.Start(ctx, "policy.deny")
	defer span.End()

	span.SetAttributes(
		attribute.String("timestamp.formatted", time.Unix(event.Timestamp, 0).Format(time.RFC3339)),
		attribute.String("node.name", t.nodeName),
		attribute.String("cni.plugin", event.CNIType),
		attribute.String("network.protocol", string(event.Protocol)),
		attribute.String("source.namespace", event.SrcNamespace),
		attribute.String("source.name", event.SrcName),
		attribute.StringSlice("source.labels", event.SrcLabels),
		attribute.StringSlice("source.workloads", event.SrcWorkloads),
		attribute.String("destination.namespace", event.DstNamespace),
		attribute.String("destination.name", event.DstName),
		attribute.StringSlice("destination.labels", event.DstLabels),
		attribute.StringSlice("destination.workloads", event.DstWorkloads),
		attribute.StringSlice("egress.enforced_by", policiesToStrings(event.EgressEnforcedBy)),
		attribute.StringSlice("ingress.enforced_by", policiesToStrings(event.IngressEnforcedBy)),
	)
}

func (t *TelemetryService) Shutdown(ctx context.Context) error {
	if t.tracerProvider == nil {
		return nil
	}

	t.log.Info("Shutting down OpenTelemetry")
	return t.tracerProvider.Shutdown(ctx)
}

// NewEndpointService creates a new endpoint service
func NewEndpointService(clientset kubernetes.Interface, log *logrus.Logger) *EndpointService {
	return &EndpointService{
		clientset: clientset,
		log:       log,
	}
}

// NewEventProcessor creates a new event processor
func NewEventProcessor(telemetry *TelemetryService, log *logrus.Logger) *EventProcessor {
	return &EventProcessor{
		telemetry: telemetry,
		log:       log,
	}
}

func (e *EventProcessor) ProcessPolicyDenyEvent(event *PolicyDenyEvent) error {
	if event == nil {
		return fmt.Errorf("event is empty")
	}

	e.log.Info("Processing policy deny event",
		"timestamp", event.Timestamp,
		"cniPlugin", event.CNIType,
		"protocol", event.Protocol,
		"srcNamespace", event.SrcNamespace,
		"srcName", event.SrcName,
		"srcLabels", event.SrcLabels,
		"srcWorkloads", event.SrcWorkloads,
		"dstNamespace", event.DstNamespace,
		"dstName", event.DstName,
		"dstLabels", event.DstLabels,
		"dstWorkloads", event.DstWorkloads,
		"egressEnforcedBy", event.EgressEnforcedBy,
		"ingressEnforcedBy", event.IngressEnforcedBy)

	e.telemetry.EmitPolicyDenyEvent(event)

	return nil
}

// NewPolicyService creates a new policy service
func NewPolicyService(config *rest.Config, log *logrus.Logger) *PolicyService {
	return &PolicyService{
		config: config,
		log:    log,
	}
}

func (p *PolicyService) GetNetworkPolicyAPIVersion(kind, name, namespace string) (string, error) {
	dynamicClient, err := dynamic.NewForConfig(p.config)
	if err != nil {
		return "", fmt.Errorf("failed to create dynamic client: %w", err)
	}

	var gvr schema.GroupVersionResource
	switch kind {
	case "NetworkPolicy":
		gvr = schema.GroupVersionResource{
			Group:    "networking.k8s.io",
			Version:  "v1",
			Resource: "networkpolicies",
		}
	case "CalicoNetworkPolicy":
		gvr = schema.GroupVersionResource{
			Group:    "projectcalico.org",
			Version:  "v3",
			Resource: "networkpolicies",
		}
	case "GlobalNetworkPolicy":
		gvr = schema.GroupVersionResource{
			Group:    "projectcalico.org",
			Version:  "v3",
			Resource: "globalnetworkpolicies",
		}
	case "CiliumNetworkPolicy":
		gvr = schema.GroupVersionResource{
			Group:    "cilium.io",
			Version:  "v2",
			Resource: "ciliumnetworkpolicies",
		}
	case "CiliumClusterwideNetworkPolicy":
		gvr = schema.GroupVersionResource{
			Group:    "cilium.io",
			Version:  "v2",
			Resource: "ciliumclusterwidenetworkpolicies",
		}
	default:
		return "", fmt.Errorf("unsupported network policy kind: %s", kind)
	}

	_, err = dynamicClient.Resource(gvr).Namespace(namespace).Get(context.Background(), name, metav1.GetOptions{})
	if err != nil {
		return "", fmt.Errorf("failed to get policy: %w", err)
	}

	return fmt.Sprintf("%s/%s", gvr.Group, gvr.Version), nil
}

func policiesToStrings(policies []Policy) []string {
	result := make([]string, len(policies))
	for i, p := range policies {
		result[i] = p.String()
	}
	return result
}

func (e *EndpointService) GetEndpointInfoByIP(ip string) (EndpointInfo, error) {
	if ip == "" {
		return EndpointInfo{}, fmt.Errorf("IP address cannot be empty")
	}

	if net.ParseIP(ip) == nil {
		return EndpointInfo{}, fmt.Errorf("invalid IP address format: %s", ip)
	}

	// Check for services with cluster IP
	services, err := e.clientset.CoreV1().Services("").List(context.Background(), metav1.ListOptions{
		FieldSelector: fmt.Sprintf("spec.clusterIP=%s", ip),
	})
	if err == nil && len(services.Items) > 0 {
		return EndpointInfo{
			Name:      services.Items[0].Name,
			Namespace: services.Items[0].Namespace,
			Labels:    []string{"type=service"},
		}, nil
	}

	// Check for services with external IPs
	if services, err := e.clientset.CoreV1().Services("").List(context.Background(), metav1.ListOptions{}); err == nil {
		for _, svc := range services.Items {
			for _, extIP := range svc.Spec.ExternalIPs {
				if extIP == ip {
					return EndpointInfo{
						Name:      svc.Name,
						Namespace: svc.Namespace,
						Labels:    []string{"type=external-service"},
					}, nil
				}
			}
		}
	}

	// Check for pods
	pods, err := e.clientset.CoreV1().Pods("").List(context.Background(), metav1.ListOptions{
		FieldSelector: fmt.Sprintf("status.podIP=%s", ip),
	})
	if err != nil {
		return EndpointInfo{}, fmt.Errorf("failed to list pods: %w", err)
	}

	if len(pods.Items) == 0 {
		return EndpointInfo{}, fmt.Errorf("no endpoint found for IP: %s", ip)
	}

	pod := pods.Items[0]
	labels := make([]string, 0, len(pod.Labels))
	for k, v := range pod.Labels {
		labels = append(labels, fmt.Sprintf("%s=%s", k, v))
	}

	return EndpointInfo{
		Name:      pod.Name,
		Namespace: pod.Namespace,
		Labels:    labels,
	}, nil
}
