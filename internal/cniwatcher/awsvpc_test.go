package cniwatcher

import (
	"context"
	"testing"
	"time"

	"github.com/neuvector/network-enforcement/internal/otel"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes/fake"
	"k8s.io/client-go/rest"
)

func TestNewAWSVPCWatcher(t *testing.T) {
	tests := []struct {
		name     string
		nodeName string
		wantErr  bool
	}{
		{
			name:     "Valid node name",
			nodeName: "test-node",
			wantErr:  false,
		},
		{
			name:     "Empty node name",
			nodeName: "",
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fakeClient := fake.NewSimpleClientset()
			config := &rest.Config{}
			log := logrus.New()
			otelService := otel.NewOpenTelemetryService(context.Background(), log)

			cfg := CNIWatcherConfig{
				Ctx:         context.Background(),
				Clientset:   fakeClient,
				Config:      config,
				Log:         log,
				NodeName:    tt.nodeName,
				CNIType:     CNITypeAWSVPC,
				OtelService: otelService,
			}
			watcher, err := NewAWSVPCWatcher(cfg)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, watcher)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, watcher)
				assert.Equal(t, CNITypeAWSVPC, watcher.CNIType)
			}
		})
	}
}

func TestAWSVPCWatcher_Start(t *testing.T) {
	tests := []struct {
		name    string
		wantErr bool
	}{
		{
			name:    "Successful start",
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fakeClient := fake.NewSimpleClientset()
			log := logrus.New()
			otelService := otel.NewOpenTelemetryService(context.Background(), log)

			watcher := &AWSVPCWatcher{
				CNIWatcherConfig: CNIWatcherConfig{
					Clientset:   fakeClient,
					Log:         log,
					NodeName:    "test-node",
					CNIType:     CNITypeAWSVPC,
					OtelService: otelService,
				},
			}

			ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
			defer cancel()
			watcher.Ctx = ctx

			err := watcher.Start()
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				// Start will likely fail due to missing log file, but that's expected in tests
				// We're mainly testing that the method doesn't panic
				assert.NoError(t, err)
			}
		})
	}
}

func TestAWSVPCWatcher_ParsePolicyDenyEvent(t *testing.T) {
	tests := []struct {
		name     string
		flowData string
		wantErr  bool
	}{
		{
			name: "Valid flow data",
			flowData: `{
				"level": "info",
				"ts": "2023-01-01T00:00:00Z",
				"logger": "test",
				"msg": "test message",
				"Src IP": "********",
				"Dest IP": "********",
				"Proto": "TCP",
				"Src Port": 12345,
				"Dest Port": 80,
				"Verdict": "DENY"
			}`,
			wantErr: false,
		},
		{
			name:     "Invalid JSON",
			flowData: `{invalid json}`,
			wantErr:  true,
		},
		{
			name:     "Empty flow data",
			flowData: "",
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fakeClient := fake.NewSimpleClientset()
			log := logrus.New()
			otelService := otel.NewOpenTelemetryService(context.Background(), log)

			watcher := &AWSVPCWatcher{
				CNIWatcherConfig: CNIWatcherConfig{
					Clientset:   fakeClient,
					Ctx:         context.Background(),
					Log:         log,
					OtelService: otelService,
				},
			}

			err := watcher.parsePolicyDenyEvent(tt.flowData)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestAWSVPCWatcher_ResolvePodOrServiceByIP(t *testing.T) {
	tests := []struct {
		name    string
		ip      string
		pods    []*corev1.Pod
		wantErr bool
	}{
		{
			name: "Valid IP address",
			ip:   "********",
			pods: []*corev1.Pod{
				{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "test-pod",
						Namespace: "default",
					},
					Status: corev1.PodStatus{
						PodIP: "********",
					},
				},
			},
			wantErr: false,
		},
		{
			name:    "Invalid IP address",
			ip:      "invalid-ip",
			pods:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fakeClient := fake.NewSimpleClientset()
			for _, pod := range tt.pods {
				_, err := fakeClient.CoreV1().Pods(pod.Namespace).Create(context.Background(), pod, metav1.CreateOptions{})
				assert.NoError(t, err)
			}

			watcher := &AWSVPCWatcher{
				CNIWatcherConfig: CNIWatcherConfig{
					Clientset: fakeClient,
					Ctx:       context.Background(),
				},
			}

			info, err := watcher.ResolvePodOrServiceByIP(tt.ip)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, info.Name)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, info.Name)
			}
		})
	}
}

func TestAWSVPCWatcher_Shutdown(t *testing.T) {
	tests := []struct {
		name    string
		wantErr bool
	}{
		{
			name:    "Successful shutdown",
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			log := logrus.New()
			otelService := otel.NewOpenTelemetryService(context.Background(), log)

			watcher := &AWSVPCWatcher{
				CNIWatcherConfig: CNIWatcherConfig{
					Log:         log,
					OtelService: otelService,
				},
			}

			err := watcher.Shutdown()
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
