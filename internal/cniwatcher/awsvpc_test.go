package cniwatcher

import (
	"context"
	"testing"
	"time"

	"github.com/go-logr/logr"
	"github.com/stretchr/testify/assert"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes/fake"
	"k8s.io/client-go/rest"
)

func TestNewAWSVPCWatcher(t *testing.T) {
	tests := []struct {
		name     string
		nodeName string
		wantErr  bool
	}{
		{
			name:     "Valid node name",
			nodeName: "test-node",
			wantErr:  false,
		},
		{
			name:     "Empty node name",
			nodeName: "",
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fakeClient := fake.NewSimpleClientset()
			config := &rest.Config{}
			log := logr.Discard()

			watcher, err := NewAWSVPCWatcher(fakeClient, config, log, tt.nodeName)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, watcher)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, watcher)
				assert.Equal(t, CNITypeAWSVPC, watcher.cniType)
			}
		})
	}
}

func TestAWSVPCWatcher_Start(t *testing.T) {
	tests := []struct {
		name    string
		wantErr bool
	}{
		{
			name:    "Successful start",
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fakeClient := fake.NewSimpleClientset()
			watcher := &AWSVPCWatcher{
				BaseWatcher: BaseWatcher{
					clientset: fakeClient,
					log:       logr.Discard(),
					nodeName:  "test-node",
					cniType:   CNITypeAWSVPC,
				},
			}

			ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
			defer cancel()

			err := watcher.Start(ctx)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestAWSVPCWatcher_ParsePolicyDenyEvent(t *testing.T) {
	tests := []struct {
		name     string
		flowData string
		wantErr  bool
	}{
		{
			name: "Valid flow data",
			flowData: `{
				"action": "DENY",
				"src_ip": "********",
				"dst_ip": "********",
				"protocol": "TCP",
				"src_port": 12345,
				"dst_port": 80
			}`,
			wantErr: false,
		},
		{
			name:     "Invalid JSON",
			flowData: `{invalid json}`,
			wantErr:  true,
		},
		{
			name:     "Empty flow data",
			flowData: "",
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			watcher := &AWSVPCWatcher{
				BaseWatcher: BaseWatcher{
					log: logr.Discard(),
				},
			}

			err := watcher.parsePolicyDenyEvent(tt.flowData)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestAWSVPCWatcher_GetEndpointInfo(t *testing.T) {
	tests := []struct {
		name    string
		ip      string
		pods    []*corev1.Pod
		wantErr bool
	}{
		{
			name: "Valid IP address",
			ip:   "********",
			pods: []*corev1.Pod{
				{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "test-pod",
						Namespace: "default",
					},
					Status: corev1.PodStatus{
						PodIP: "********",
					},
				},
			},
			wantErr: false,
		},
		{
			name:    "Invalid IP address",
			ip:      "invalid-ip",
			pods:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fakeClient := fake.NewSimpleClientset()
			for _, pod := range tt.pods {
				_, err := fakeClient.CoreV1().Pods(pod.Namespace).Create(context.Background(), pod, metav1.CreateOptions{})
				assert.NoError(t, err)
			}

			watcher := &AWSVPCWatcher{
				BaseWatcher: BaseWatcher{
					clientset: fakeClient,
					log:       logr.Discard(),
				},
			}

			info, err := watcher.BaseWatcher.getEndpointInfoByIP(tt.ip)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, info)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, info)
			}
		})
	}
}

func TestAWSVPCWatcher_Shutdown(t *testing.T) {
	tests := []struct {
		name    string
		wantErr bool
	}{
		{
			name:    "Successful shutdown",
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fakeClient := fake.NewSimpleClientset()
			watcher := &AWSVPCWatcher{
				BaseWatcher: BaseWatcher{
					clientset: fakeClient,
					log:       logr.Discard(),
				},
			}

			ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
			defer cancel()

			err := watcher.Shutdown(ctx)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
