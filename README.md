
# Network Enforcement

This repository contains the source code and documentation for the `netEnforcer`, a component designed to enhance network security observability and enforcement within Kubernetes environments.

## Overview

The `netEnforcer` acts as a centralized point for collecting, processing, and acting upon network traffic data and policy violations. It integrates with various data sources, including the SUSE Observability API, CNI plugins (Calico, Cilium, Amazon VPC CNI, Flannel), and Kubernetes itself, to provide comprehensive network security insights and control.

## Features

* **Connection Report Processing:**
    * Queries the SUSE Observability API for connection reports
    * Transforms raw connection reports into structured conversation reports
    * Stores conversation reports in a storage for display and analysis purpose
* **Custom Resource Management:**
    * Defines and manages Custom Resource Definition (CRD) for Group and Security Proposal resources.
    * Provides a CR controller for CRUD operations on Group and Security Proposal CR
    * Watches pod join/leave events to update group membership
    * Accepts user created Group and Security Proposal CR from UI extension
* **Discovery Mode:**
    * Learns network traffic patterns from connection reports
    * Generates Kubernetes Custom Resources (CRs) for Group and Security Proposals
        * **Group CR:** Define logical groups of pods in Kubernetes cluster environment
        * **Security Proposal CR:** Propose network security policies based on learned groups and connection reports
    * Automation of internal NetworkRule and Kubernetes NetworkPolicy generation
* **Monitor Mode:**
    * Matches connection reports against defined internal NetworkRules
    * Marks policy violations and generates informative alert messages
    * Deliver alert messages to SecurityHub
* **Protect Mode:**
    * Generates Kubernetes NetworkPolicy based on Security Proposal CR
    * Apply Kubernetes NetworkPolicy
* **CNI Integration:**
    * Collects and processes network policy verdict deny logs from various CNI plugins (Calico, Cilium, Amazon VPC CNI, Flannel)
    * Provides a unified violation report format
* **SecurityHub Integration:**
    * Sends Conversation Reports, CNI Violation Reports, and Monitor Mode Alert Messages to SecurityHub

## Architecture

The `netEnforcer` operates within a Kubernetes cluster and interacts with various components:

* **SUSE Observability API:** Retrieves connection reports
* **CNI Plugins:** Collects network policy verdict deny logs
* **Kubernetes API:** Manages CRD and NetworkPolicy
* **Storage:** Stores conversation reports
* **SecurityHub:** Sends security event data
* **UI Extension:** Receives user created Group and Security Proposal CR

## Getting Started

### Prerequisites

* Kubernetes cluster (version X.Y or later)
* CNI plugins (Calico, Cilium, Amazon VPC CNI, Flannel) installed in your cluster

### Installation

1.  Clone the repository:

    ```bash
    git clone [repository URL]
    ```

2.  Build the `netEnforcer`:

    ```bash
    # Build instructions here
    ```

3.  Deploy the `netEnforcer` to your Kubernetes cluster:

    ```bash
    # Deployment instructions here (e.g., using kubectl apply -f deployment.yaml)
    ```

### Configuration

The `netEnforcer` can be configured using environment variables or configuration files. Refer to the `config/` directory for example configuration files.

## License

This project is licensed under the [License Name] license. See the `LICENSE` file for details.
