cniwatcher:
  # CNI type must be specified
  # Valid values: cilium, calico, flannel, aws-vpc
  cniType: "cilium"
  namespace: kube-system
  image:
    repository: nvpublic/up
    tag: kdong
    pullPolicy: Always
  otelEndpoint: "otel-collector.otel.svc.cluster.local:4317"
  cilium:
    hubbleEndpoint: "unix:///var/run/cilium/hubble.sock"
  calico:
    goldmaneEndpoint: "goldmane.calico-system.svc:7443"
    namespace: calico-system
  volumeMounts: []
  volumes: []
