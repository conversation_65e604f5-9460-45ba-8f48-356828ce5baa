{{/*
Set appropriate namespace based on CNI type
*/}}
{{- define "network-enforcement.cniwatcher.namespace" -}}
{{- if eq .Values.cniwatcher.cniType "calico" -}}
{{- .Values.cniwatcher.calico.namespace | default "calico-system" -}}
{{- else -}}
{{- .Values.cniwatcher.namespace | default "kube-system" -}}
{{- end -}}
{{- end -}}

{{/*
CNI-specific volume mounts for network-enforcement.cniwatcher
*/}}
{{- define "network-enforcement.cniwatcher.volumeMounts" -}}
{{- if eq .Values.cniwatcher.cniType "cilium" }}
- name: hubble-sock
  mountPath: /var/run/cilium
{{- else if eq .Values.cniwatcher.cniType "calico" }}
- name: goldmane-key-pair-volume
  mountPath: /etc/goldmane/certs
  readOnly: true
{{- else if eq .Values.cniwatcher.cniType "flannel" }}
- name: flannel-ulog
  mountPath: /var/log/ulog/syslogemu.log
  readOnly: true
{{- else if eq .Values.cniwatcher.cniType "aws-vpc" }}
- name: aws-eni-logs
  mountPath: /var/log/aws-routed-eni/network-policy-agent.log
  readOnly: true
{{- end }}
{{- end -}}

{{/*
CNI-specific volumes for network-enforcement.cniwatcher
*/}}
{{- define "network-enforcement.cniwatcher.volumes" -}}
{{- if eq .Values.cniwatcher.cniType "cilium" }}
- name: hubble-sock
  hostPath:
    path: /var/run/cilium
{{- else if eq .Values.cniwatcher.cniType "calico" }}
- name: goldmane-key-pair-volume
  secret:
    secretName: goldmane-key-pair
{{- else if eq .Values.cniwatcher.cniType "flannel" }}
- name: flannel-ulog
  hostPath:
    path: /var/log/ulog/syslogemu.log
    type: File
{{- else if eq .Values.cniwatcher.cniType "aws-vpc" }}
- name: aws-eni-logs
  hostPath:
    path: /var/log/aws-routed-eni/network-policy-agent.log
    type: File
{{- end }}
{{- end -}}