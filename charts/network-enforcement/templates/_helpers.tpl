{{/*
Expand the name of the chart.
*/}}
{{- define "network-enforcement.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "network-enforcement.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "network-enforcement.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "network-enforcement.labels" -}}
helm.sh/chart: {{ include "network-enforcement.chart" . }}
{{ include "network-enforcement.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "network-enforcement.selectorLabels" -}}
app.kubernetes.io/name: {{ include "network-enforcement.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Set appropriate namespace based on CNI type
*/}}
{{- define "network-enforcement.cniwatcher.namespace" -}}
{{- if eq .Values.cniwatcher.cniType "calico" -}}
{{- .Values.cniwatcher.calico.namespace | default "calico-system" -}}
{{- else -}}
{{- .Values.cniwatcher.namespace | default "kube-system" -}}
{{- end -}}
{{- end -}}

{{/*
CNI-specific volume mounts for network-enforcement.cniwatcher
*/}}
{{- define "network-enforcement.cniwatcher.volumeMounts" -}}
{{- if eq .Values.cniwatcher.cniType "cilium" }}
- name: hubble-sock
  mountPath: /var/run/cilium
{{- else if eq .Values.cniwatcher.cniType "calico" }}
- name: goldmane-key-pair-volume
  mountPath: /etc/goldmane/certs
  readOnly: true
{{- else if eq .Values.cniwatcher.cniType "flannel" }}
- name: flannel-ulog
  mountPath: /var/log/ulog/syslogemu.log
  readOnly: true
{{- else if eq .Values.cniwatcher.cniType "aws-vpc" }}
- name: aws-eni-logs
  mountPath: /var/log/aws-routed-eni/network-policy-agent.log
  readOnly: true
{{- end }}
{{- end -}}

{{/*
CNI-specific volumes for network-enforcement.cniwatcher
*/}}
{{- define "network-enforcement.cniwatcher.volumes" -}}
{{- if eq .Values.cniwatcher.cniType "cilium" }}
- name: hubble-sock
  hostPath:
    path: /var/run/cilium
{{- else if eq .Values.cniwatcher.cniType "calico" }}
- name: goldmane-key-pair-volume
  secret:
    secretName: goldmane-key-pair
{{- else if eq .Values.cniwatcher.cniType "flannel" }}
- name: flannel-ulog
  hostPath:
    path: /var/log/ulog/syslogemu.log
    type: File
{{- else if eq .Values.cniwatcher.cniType "aws-vpc" }}
- name: aws-eni-logs
  hostPath:
    path: /var/log/aws-routed-eni/network-policy-agent.log
    type: File
{{- end }}
{{- end -}}