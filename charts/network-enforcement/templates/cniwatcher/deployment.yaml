apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: {{ include "network-enforcement.fullname" . }}-cniwatcher
  namespace: {{ include "network-enforcement.cniwatcher.namespace" . }}
  labels:
    app.kubernetes.io/name: {{ include "network-enforcement.name" . }}-cniwatcher
    app.kubernetes.io/part-of: {{ include "network-enforcement.name" . }}
    {{- include "network-enforcement.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "network-enforcement.name" . }}-cniwatcher
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ include "network-enforcement.name" . }}-cniwatcher
        app.kubernetes.io/instance: {{ .Release.Name }}
        app.kubernetes.io/part-of: {{ include "network-enforcement.name" . }}
    spec:
      serviceAccountName: {{ include "network-enforcement.fullname" . }}-cniwatcher
      containers:
      - name: cniwatcher
        image: "{{ .Values.cniwatcher.image.repository }}:{{ .Values.cniwatcher.image.tag }}"
        imagePullPolicy: {{ .Values.cniwatcher.image.pullPolicy }}
        env:
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: OTEL_EXPORTER_OTLP_ENDPOINT
          value: {{ .Values.cniwatcher.otelEndpoint | quote }}
        - name: CNI_TYPE
          value: {{ .Values.cniwatcher.cniType | quote }}
        {{- if eq .Values.cniwatcher.cniType "cilium" }}
        - name: HUBBLE_ENDPOINT
          value: {{ .Values.cniwatcher.cilium.hubbleEndpoint | default "unix:///var/run/cilium/hubble.sock" | quote }}
        {{- end }}
        {{- if eq .Values.cniwatcher.cniType "calico" }}
        - name: GOLDMANE_ENDPOINT
          value: {{ .Values.cniwatcher.calico.goldmaneEndpoint | default "goldmane.calico-system.svc:7443" | quote }}
        {{- end }}
        ports:
        - containerPort: 8765
          name: http
          protocol: TCP
        volumeMounts:
        {{- include "network-enforcement.cniwatcher.volumeMounts" . | nindent 8 }}
        {{- if .Values.cniwatcher.volumeMounts }}
        {{- toYaml .Values.cniwatcher.volumeMounts | nindent 8 }}
        {{- end }}
      volumes:
      {{- include "network-enforcement.cniwatcher.volumes" . | nindent 6 }}
      {{- if .Values.cniwatcher.volumes }}
      {{- toYaml .Values.cniwatcher.volumes | nindent 6 }}
      {{- end }}
