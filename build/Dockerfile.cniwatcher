#
# Builder
#
FROM registry.suse.com/bci/golang:1.24 AS builder

WORKDIR /workspace
COPY go.mod go.mod
COPY go.sum go.sum
COPY cmd/ cmd/
COPY pkg/ pkg/

RUN CGO_ENABLED=0 go build -a -o cniwatcher cmd/cniwatcher/main.go

#
# Base image
# 
FROM registry.suse.com/bci/bci-micro:15.6 AS micro
FROM registry.suse.com/bci/bci-base:15.6 AS base

COPY --from=micro / /chroot/
RUN zypper --installroot /chroot clean -a && \
    rm -rf /chroot/var/log/

#
# Artifact
#
FROM micro

WORKDIR /
COPY --from=base /chroot/ /
COPY --from=builder /workspace/cniwatcher /

ENTRYPOINT ["/cniwatcher"]
